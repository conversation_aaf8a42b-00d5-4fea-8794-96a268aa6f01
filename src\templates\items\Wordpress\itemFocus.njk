{% raw %}
    <?php if ( ! empty( $args['pages'] ) ): ?>
        <?php foreach ( $args['pages'] as $page ): ?>
            <?php if ( ! empty( $page['page_image'] ) ): ?>
                <row>
                    <columns class="first last section-image">
                        <center>
                            <a href="<?php echo esc_url( $page['page_permalink'] ); ?>">
                                <img src="<?php echo esc_url( $page['page_image'] ); ?>" alt="">
                            </a>
                        </center>
                    </columns>
                </row>
            <?php endif; ?>
            <row>
                <columns class="first last section-content">
                    <?php if ( ! empty( $page['page_surtitle'] ) ): ?>
                        <p class="section__subtitle"><a href="<?php echo esc_url( $page['page_permalink'] ); ?>"><?php echo esc_html( $page['page_surtitle'] ); ?></a></p>
                        <spacer size="5"></spacer>
                    <?php endif; ?>
                    <h3 class="section__title"><a href="<?php echo esc_url( $page['page_permalink'] ); ?>"><?php echo esc_html( $page['page_title'] ); ?></a></h3>
                    <?php if ( ! empty( $page['page_lead_text'] ) ): ?>
                        <spacer size="15"></spacer>
                        <p class="section__teaser"><a href="<?php echo esc_url( $page['page_permalink'] ); ?>"><?php echo esc_html( $page['page_lead_text'] ); ?></a></p>
                    <?php endif; ?>
                </columns>
            </row>
        <?php endforeach; ?>
    <?php endif; ?>
{% endraw %}
