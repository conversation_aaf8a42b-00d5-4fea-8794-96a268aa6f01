{% raw %}
    <table role="presentation" class="footer-social">
        <tr>
            <?php
                $social_networks = $args['social_networks'];
                $networks        = $args['networks'];
                $first           = true;
                foreach ( $social_networks as $network ) :
                    $network_info  = $networks[ $network['social_network'] ];
                    $network_type  = $network['social_network'];
                    $network_url   = sprintf( $network_info['url'], $network[ $network_type ] );
                    $network_label = empty( $network['label'] ) ? $network_info['label'] : $network['label'];
                    $network_icon  = $network_info['newsletter-icon'];
                    ?>
            <td  class="footer-social__item  <?php if ( $first ) { echo esc_attr( '-first' ); } ?>  ">
                <a href="<?php echo esc_url( $network_url ); ?>" title="<?php echo esc_attr( $network_label ); ?>" target="_blank">
                    <img src="<?php echo esc_url( get_theme_file_uri( 'images/newsletter/social/' . $network_icon . '.png' ) ); ?>" alt="<?php echo esc_attr( $network_label ); ?>">
                </a>
            </td>
            <?php
                $first = false;
            endforeach;
            ?>
        </tr>
    </table>
{% endraw %}
