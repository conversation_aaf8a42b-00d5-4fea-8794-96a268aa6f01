{% raw %}
    <?php if ( ! empty( $args['news'] ) || ! empty ( $args['focus_posts'] ) ): ?>
    <container>
        <?php if ( ! empty( $args['focus_posts'] ) ): ?>
        <?php foreach( $args['focus_posts'] as $news ): ?>
            <row>
            <columns class="news-focus first last">
                <?php if ( ! empty ( $news['news_permalink'] ) ): ?>
                <a href="<?php echo esc_url( $news['news_permalink'] ); ?>" aria-hidden="true" tabindex="-1">
                    <center>
                        <img src="<?php echo esc_url( $news['news_focus_image'] ); ?>" alt=""/>
                    </center>
                </a>
                <?php else: ?>
                    <center>
                        <img src="<?php echo esc_url( $news['news_focus_image'] ); ?>" alt=""/>
                    </center>
                <?php endif; ?>
                <spacer size="20"></spacer>
                <table role="presentation" class="category">
                    <tr>
                        <td>
                            <?php echo $news['news_terms_blockpost']; ?>
                        </td>
                    </tr>
                </table>
                <spacer size="8"></spacer>
                <h3 class="section__title">
                    <?php if ( ! empty ( $news['news_permalink'] ) ): ?>
                        <a href="<?php echo esc_url( $news['news_permalink'] ); ?>" title="<?php echo esc_attr( $news['news_title'] ); ?>"><?php echo esc_html( $news['news_title'] ); ?></a>
                    <?php else: ?>
                        <?php echo esc_html( $news['news_title'] ); ?>
                    <?php endif; ?>
                </h3>
                <spacer size="15"></spacer>
                <?php if ( !empty ( $news['news_lead_text'] ) ): ?>
                <p class="section__teaser"><?php echo esc_html( $news['news_lead_text'] ); ?></p>
                <?php endif; ?>
            </columns>
        </row>
            <?php endforeach; ?>
        <?php endif; ?>
        <?php foreach( $args['news'] as $news ): ?>
        <row>
            <columns large="6" class="first">
                <?php if ( ! empty ( $news['news_permalink'] ) ): ?>
                <a href="<?php echo esc_url( $news['news_permalink'] ); ?>" aria-hidden="true" tabindex="-1">
                    <center>
                        <img src="<?php echo esc_url( $news['news_image'] ); ?>" alt=""/>
                    </center>
                </a>
                <?php else: ?>
                <center>
                    <img src="<?php echo esc_url( $news['news_image'] ); ?>" alt=""/>
                </center>
                <?php endif; ?>
            </columns>
            <columns large="6" class="last">
                <table role="presentation" class="category">
                    <tr>
                        <td>
                            <?php echo $news['news_terms_blockpost']; ?>
                        </td>
                    </tr>
                </table>
                <spacer size="10"></spacer>
                <h3 class="section__title">
                    <?php if ( ! empty ( $news['news_permalink'] ) ): ?>
                         <a href="<?php echo esc_url( $news['news_permalink'] ); ?>" title="<?php echo esc_attr( $news['news_title'] ); ?>"><?php echo esc_html( $news['news_title'] ); ?></a>
                    <?php else: ?>
                        <?php echo esc_html( $news['news_title'] ); ?>
                    <?php endif; ?>
                </h3>
                <spacer size="15"></spacer>
                <?php if ( !empty ( $news['news_lead_text'] ) ): ?>
                <p class="section__teaser"><?php echo esc_html( $news['news_lead_text'] ); ?></p>
                <?php endif; ?>
            </columns>
        </row>
        <spacer size="40"></spacer>
        <?php endforeach; ?>
    </container>
    <?php endif; ?>
{% endraw %}
