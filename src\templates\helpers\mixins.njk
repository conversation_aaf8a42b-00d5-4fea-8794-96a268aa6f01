{% macro includeCss(bool) %}
{%- if not bool -%}
    <link rel="stylesheet" type="text/css" href="app.css">
    {%- if not isBE -%}
    <!--[if mso]>
    <style type="text/css" data-embed="true">
        body, table, td, h1, h2, h3, h4, div, span, p, a {font-family: Arial, Helvetica, sans-serif !important;}
    </style>
    <![endif]-->
    {%- endif -%}
{%- endif -%}
{% endmacro %}

{% macro sectionTitle(title, propose = false) %}
    <container class="section-title">
        <row>
            <columns large="7" class="first {{ 'last' if not propose }}">
                <left>
                    <h2>{{ title }}</h2>
                    <hr align="left" width="49" size="9" aria-hidden="true">
                </left>
            </columns>
            {%- if propose -%}
                <columns large="5" class="last" valign="middle">
                    <p class="text-right small-text-left"><a class="propose" href="{{ propose }}"><img src="{{ imgPath }}icons/calendar-plus.png" alt=""> Proposer un événement</a></p>
                </columns>
            {%- endif -%}
        </row>
    </container>
{% endmacro %}

{% macro sectionButton(title, link, color='red', eventsBtn) %}
    {% if isTYPO3 %}<f:if condition="{{ link }}">{% elseif isDRUPAL %}{% raw %}{% if link_list %}{% endraw %}{% endif %}
    <container class="section-button">
        <row>
            <columns class="first last section-button-column set-right-justified">
                <center>
                    <table class="button large float-center" role="presentation">
                        <tbode>
                            <tr>
                                <td>
                                    <table role="presentation">
                                        <tr>
                                            {% if eventsBtn %}
                                                <td class="events-btn">
                                                    <a href="{{ link }}" align="center">Proposer un événement</a>
                                                </td>
                                                <td style="width: 15px;"></td>
                                            {% endif %}
                                            <td class="button-wrap">
                                                <a href="{{ link }}" align="center">{{ title }}</a>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </tbode>
                    </table>
                </center>
            </columns>
        </row>
    </container>
    {% if isTYPO3 %}</f:if>{% elseif isDRUPAL %}{% raw %}{% endif %}{% endraw %}{% endif %}
{% endmacro %}

{%- macro zeroCheck(num=0) -%}
    {{- '0'+num if num < 9 else num -}}
{%- endmacro -%}

{%- macro checkDefault(num=0, rand=1) -%}
    {{- num if num else range(1, rand) | random -}}
{%- endmacro -%}

{%- macro randTime(hour='', minute='') -%}
    {{- zeroCheck(checkDefault(hour, 24)) -}}:{{- zeroCheck(checkDefault(minute, 59)) -}}
{%- endmacro -%}

{%- macro randDate(day='', month='', year='2018') -%}
    {{- zeroCheck(checkDefault(day, 28)) }}-{{ zeroCheck(checkDefault(month, 12)) }}-{{ zeroCheck(checkDefault(year, 2019)) -}}
{%- endmacro -%}
