{% import "helpers/mixins.njk" as mixins with context -%}
{{ mixins.includeCss(isTemplateIncluded) }}
{% set quickAccess = {
    title: "{title}||{{ title_block }}||<?php echo esc_html( $args['block_title'] ); ?>||Accès rapides"
} | parseBE -%}
<center>
    <wrapper class="section quick-access">
        <container class="section-title">
            <row>
                <columns large="7" class="first last">
                    <h2 class="section__subtitle">{{ quickAccess.title }}</h2>
                </columns>
            </row>
        </container>
        {% include "items/{{ env }}/itemQuickAccess.njk" | interpolate -%}
    </wrapper>
</center>
