.header-topline {
    background: $color-1--2;

    .container {
        background: none;

        .columns {
            padding-bottom: 8px;
            padding-top: 8px;

            @media only screen and (max-width: #{$global-breakpoint}) {
                padding-bottom: 12px !important;
                padding-left: 40px !important;
                padding-right: 40px !important;
                padding-top: 11px !important;
            }
        }
    }

    &__text {
        color: $color-white;
        font-size: 13px;
        font-weight: 400;
        margin-bottom: 0;
        Margin-bottom: 0;

        a {
            color: $color-white;
            font-weight: 700;
            text-decoration: underline;
        }

        @media only screen and (max-width: #{$global-breakpoint}) {
            font-size: 11px !important;
            line-height: 12px !important;
        }
    }
}

.header {
    background: $color-white;

    .container {
        background: none;
    }

    hr {
        display: none;
        
        @media only screen and (max-width: #{$global-breakpoint}) {
        background-color: $color-1--1;
            border-bottom: 1px solid $color-1--1;
            border-left: 1px solid $color-1--1;
            border-right: 1px solid $color-1--1;
            border-top: 1px solid $color-1--1;
            color: $color-1--1;
            display: block !important;
            height: 1px !important;
        }
    }

    .spacer {
        @media only screen and (max-width: #{$global-breakpoint}) {
            display: none !important;
        }
    }

    // logo
    th.first.header-image {
        padding-bottom: 0;
        padding-left: 0 !important;
        padding-right: 0;
        padding-top: 0;
        vertical-align: top;
        width: 283px;

        @media only screen and (max-width: #{$global-breakpoint}) {
            padding-bottom: 0 !important;
            padding-right: 0 !important;
            padding-top: 15px !important;
        }

        img {
            width: 332px !important;
            height: 124px !important;

            @media only screen and (max-width: #{$global-breakpoint}) {
                height: 103px !important;
                margin: 0 auto !important;
                width: 274px !important;
                padding-bottom: 22px;
                padding-top: 11px;
            }
        }

        th {
            @media only screen and (max-width: #{$global-breakpoint}) {
                padding-left: 15px !important;
                padding-right: 15px !important;
            }
        }
    }

    th.last.header-title table {
        border-left: 3px solid $color-1--1 !important;

        @media only screen and (max-width: #{$global-breakpoint}) {
            border-left: 0 !important;
        }
    }

    th.last.header-title table h1 {
        padding-left: 20px !important;
        padding-top: 14px;
        padding-bottom: 11px;

        @media only screen and (max-width: #{$global-breakpoint}) {
            padding-left: 0 !important;
        }
    }

    th.last.header-title {
        margin-bottom: 14px;
        padding-bottom: 0;
        padding-left: 0;
        padding-right: 0;
        padding-top: 13px;
        width: -zf-grid-calc-px(5, $grid-column-count, $global-width) - 24;

        @media only screen and (max-width: #{$global-breakpoint}) {
            border-left: 0 !important;
            padding-bottom: 0 !important;
            padding-left: 53px !important;
            padding-right: 53px !important;
            padding-top: 0 !important;
        }

        hr {
            background-color: $color-white;
            border-bottom: 1px solid $color-white;
            border-left: 1px solid $color-white;
            border-right: 1px solid $color-white;
            border-top: 2px solid $color-white;
            color: $color-white;
            display: none;
            height: 1px !important;
            margin: 0;

            @media only screen and (max-width: #{$global-breakpoint}) {
                //display: block !important;
                margin-bottom: 15px !important;
                Margin-bottom: 15px !important;
            }
        }

        th {
            @media only screen and (max-width: #{$global-breakpoint}) {
                border-top: 3px solid $color-white !important;
                padding-bottom: 21px !important;
                padding-top: 6px !important;
                padding-left: 0 !important;
                padding-right: 0 !important;
            }
        }

        h1 {
            color: $color-1--2;
            font-size: 25px;
            font-weight: 400;
            line-height: (29/25);
            margin-bottom: 0;
            Margin-bottom: 0;

            strong {
                font-weight: 700;
            }

            span {
                color: $color-1--2;
                display: block;
                font-size: 16px;
                margin-bottom: 0;
                Margin-bottom: 0;
                text-transform: uppercase;

                @media only screen and (max-width: #{$global-breakpoint}) {
                    font-size: 16px !important;
                    text-align: center !important;
                }
            }

            @media only screen and (max-width: #{$global-breakpoint}) {
                font-size: 25px !important;
                text-align: center !important;
            }
        }
    }
}
