{% raw %}
    <f:if condition="{news}">
        <container>
            <f:for each="{news}" as="newsItem">
                <row>
                    {n:link(newsItem:newsItem,settings:settings,uriOnly:'1') -> v:variable.set(name:'relativeLinkToSingle')}
                    {f:uri.typolink(parameter:relativeLinkToSingle,absolute:1) -> v:variable.set(name:'linkToSingle')}
                    <f:if condition="{newsItem.mainMedia} || {newsItem.falMedia}">
                        <columns large="8" class="first">
                            <a href="{linkToSingle}" aria-hidden="true" tabindex="-1">
                                <img src="{f:render(partial:'Common/ListImageNewsletter', section:'MainOrFalImage', arguments:'{newsItem:newsItem,width:\'410c\',height:\'270c\'}') -> v:format.trim()}" alt=""/>
                            </a>
                        </columns>
                    </f:if>
                    <columns large="4" class="last">
                        <f:if condition="{newsItem.categories}">
                            <table role="presentation" class="category">
                                <tr>
                                    <td>{v:render.template(file:'EXT:stratis_site/Resources/Private/Partials/Common/InlineList.html',variables:'{records:newsItem.categories, glue:\', \'}')}</td>
                                </tr>
                            </table>
                            <spacer size="5"></spacer>
                        </f:if>
                        <h3 class="section__title">
                            <a href="{linkToSingle}">{newsItem.title}</a>
                        </h3>
                        <spacer size="10"></spacer>
                        <f:if condition="{newsItem.teaser} || {newsItem.bodytext}">
                            <p class="section__teaser">
                                <a href="{linkToSingle}">
                                    {f:render(partial:'Extensions/news/List/Common/DisplayTeaser', section:'DisplayTeaser', arguments:'{newsItem:newsItem, settings:settings}')}
                                </a>
                            </p>
                            <spacer size="10"></spacer>
                        </f:if>
                        <table role="presentation">
                            <tr>
                                <td style="width: 26px;">
                                    <img src="{f:uri.image(absolute:1,src:'{pathToImages}newsletter/icons/camera.png')}" alt="">
                                </td>
                                <td class="albums__counter">{f:render(partial:'Extensions/news/Common/DisplayNumberOfMedias', section:'DisplayNumberOfMedias', arguments:'{newsItem:newsItem, settings:settings}')}</td>
                            </tr>
                        </table>
                    </columns>
                </row>
                <spacer size="34"></spacer>
            </f:for>
        </container>
    </f:if>
{% endraw %}
