const fs = require('fs');
const path = require('path');
const opn = require('open');
const $ = require('./helpers');

const tmpl = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Document</title>
    <style data-embed="true">
        html,
        body {
            height: 100%;
        }

        body {
            display: flex;
            margin: 0;
        }

        iframe {
            width: 50%;
        }
    </style>
</head>
<body>
    <iframe src="http://localhost:3010" frameborder="1"></iframe>
    <iframe src="http://localhost:3020" frameborder="1"></iframe>
</body>
</html>`;

const filePath = path.join(__dirname, '../build2/double.html');

const openFile = async () => {
    await opn(filePath);
};

const open = (done) => {
    if (!$.IS_DOUBLE) {
        return done();
    }

    if (fs.existsSync(filePath)) {
        openFile();
    } else {
        fs.writeFile(filePath, tmpl, openFile);
    }

    return done();
};

module.exports = open;
