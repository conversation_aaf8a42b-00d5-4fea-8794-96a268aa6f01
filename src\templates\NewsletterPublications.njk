{% import "helpers/mixins.njk" as mixins with context -%}
{{- mixins.includeCss(isTemplateIncluded) -}}
{% set publications = {
    title: '{title}||{{ title }}||<?php echo esc_html( $args[\'block_title\'] ); ?>||A lire',
    footerLink: '{allLink}||{{ link_list }}||<?php echo esc_url( $args[\'archive_link\'] ); ?>||#',
    footerText: '{allLinkTitle}||Toutes les publications||<?php echo esc_html( $args[\'archive_link_title\'] ); ?>||Toutes les publications'
} | parseBE -%}
<center>
    <wrapper class="section publications">
        {{- mixins.sectionTitle(publications.title) -}}
        {% include "items/{{ env }}/itemPublications.njk" | interpolate -%}
        {{- mixins.sectionButton(publications.footerText, publications.footerLink) -}}
    </wrapper>
</center>
