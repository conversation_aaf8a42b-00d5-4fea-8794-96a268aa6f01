{% raw %}
        <container>
            <row>
                <columns large="8" class="first">
                    <a href="<?php echo esc_url( $args['album_permalink'] ); ?>" aria-hidden="true" tabindex="-1">
                        <img src= "<?php echo $args['album_image']; ?>" width="410" height="270" alt="">
                    </a>
                </columns>
                <columns large="4" class="last">
                    <table role="presentation" class="category">
                        <tr>
                            <td>
                                <?php echo $args['album_terms_blockpost']; ?>
                            </td>
                        </tr>
                    </table>
                    <spacer size="5"></spacer>
                    <h3 class="section__title">
                        <a href="<?php echo esc_url( $args['album_permalink'] ); ?>"><?php echo esc_html( $args['album_title'] ); ?></a>
                    </h3>
                    <spacer size="10"></spacer>
                    <p class="section__teaser">
                        <a href="<?php echo esc_url( $args['album_permalink'] ); ?>">
                            <?php echo esc_html( $args['album_lead_text'] ); ?>
                        </a>
                    </p>
                    <spacer size="10"></spacer>
                    <table role="presentation">
                        <tr>
                            <td style="width: 26px;">
                                <img src="<?php echo esc_url( $args['album_additional_image'] ); ?>" alt="">
                            </td>
                            <td class="albums__counter">
                                <?php echo esc_html( $args['album_item_count_by_types'] ); ?>
                            </td>
                        </tr>
                    </table>
                </columns>
            </row>
            <spacer size="34"></spacer>
        </container>
{% endraw %}
