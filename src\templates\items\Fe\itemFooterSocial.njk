{% set footerSocial = {
    socials: [
        {
            icon: '{imgPathFacebook}||#||#||{{ imgPath }}icons/social/facebook-f.png',
            link: '{linkFacebook}||#||#||#',
            title: '{titleLinkFacebook}||#||#||Facebook'
        }, {
            icon: '{imgPathTwitter}||#||#||{{ imgPath }}icons/social/twitter.png',
            link: '{linkTwitter}||#||#||#',
            title: '{titleLinkTwitter}||#||#||Twitter'
        }, {
            icon: '{imgPathInstagram}||#||#||{{ imgPath }}icons/social/youtube.png',
            link: '{linkInstagram}||#||#||#',
            title: '{titleLinkInstagram}||#||#||YouTube'
        },{
            icon: '{imgPathInstagram}||#||#||{{ imgPath }}icons/social/instagram.png',
            link: '{linkInstagram}||#||#||#',
            title: '{titleLinkInstagram}||#||#||Instagram'
        }
    ]
} | parseBE %}

<table role="presentation" class="footer-social">
    <tr>
        {%- for social in footerSocial.socials %}
            <td class="footer-social__item {{ '-first' if loop.index === 1 }}">
                <a href="{{ social.link }}" target="_blank" title="Notre {{ social.title }} nouvelle fenêtre">
                    <img src="{{ social.icon }}" alt="Notre {{ social.title | safe }}">
                </a>
            </td>
        {%- endfor %}
        <td>&nbsp;</td>
    </tr>
</table>
