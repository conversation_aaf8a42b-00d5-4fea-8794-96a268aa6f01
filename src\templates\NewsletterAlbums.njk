{% import "helpers/mixins.njk" as mixins with context -%}
{{- mixins.includeCss(isTemplateIncluded) -}}
{% set albums = {
    title: '{title}||{{ title }}||<?php echo esc_html( $args[\'block_title\'] ); ?>||Voir et revoir',
    footerLink: '{allLink}||{{ link_list }}||<?php echo esc_url( $args[\'archive_link\'] ); ?>||#',
    footerText: '{allLinkTitle}||Tous les Albums||<?php echo esc_html( $args[\'archive_link_title\'] ); ?>||Tous les Albums'
} | parseBE -%}
<center>
    <wrapper class="section albums">
        {{- mixins.sectionTitle(title = albums.title) -}}

        {% include "items/{{ env }}/itemAlbums.njk" | interpolate -%}

        {{- mixins.sectionButton(albums.footerText, albums.footerLink) -}}
    </wrapper>
</center>
