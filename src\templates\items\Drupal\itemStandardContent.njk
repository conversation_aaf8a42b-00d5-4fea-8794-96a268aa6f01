<container class="section-item">
    {% raw %}
    {% for item in array['#items'] %}
    {% set item = array[loop.index0]['#paragraph'] %}
    {% set title = item.field_title.0.value %}
    {% set subtitle = item.field_subtitle.0.value %}
    {% set body = item.field_body.0.value %}
    {% set image = drupal_field('field_media_image', 'paragraph', item.id()).0['#image_url'] %}
    {% set image_alt = drupal_field('field_media_image', 'paragraph', item.id()).0['#alt'] %}
    {% set link = item.field_link.0.getUrl().toString()|path_to_absolute %}
        <row>
            {% if image %}
                <columns large="6" class="first section-image">
                    {% if link %}
                        <a href="{{ link }}">
                            <img src="{{ image }}" {% if image_alt %} alt="{{ image_alt }}" {%else%}alt{%endif%}>
                        </a>
                    {% else %}
                        <img src="{{ image }}" {% if image_alt %} alt="{{ image_alt }}" {%else%}alt{%endif%}>
                    {% endif %}
                </columns>
                <columns large="6" class="last section-content">
                    {% if title %}
                        <h3 class="section__title">
                            {% if link %}
                                <a href="{{ link }}">{{ title }}</a>
                            {% else %}
                                {{ title }}
                            {% endif %}
                        </h3>
                        <spacer size="15"></spacer>
                    {% endif %}
                    {% if body %}
                        <p class="section__teaser">
                            {% if link %}
                                <a href="{{ link }}">{{ body|replace({"<p>" : "","</p>" : "</br>"})|truncate(200, true, true)|raw }}</a>
                            {% else %}
                                {{ body|replace({"<p>" : "","</p>" : "</br>"})|truncate(200, true, true)|raw }}
                            {% endif %}
                        </p>
                    {% endif %}
                </columns>
            {% else %}
                <columns large="12" class="first last section-content">
                    {% if title %}
                        <h3 class="section__title">
                            {% if link %}
                                <a href="{{ link }}">{{ title }}</a>
                            {% else %}
                                {{ title }}
                            {% endif %}
                        </h3>
                        <spacer size="15"></spacer>
                    {% endif %}
                    {% if body %}
                        <p class="section__teaser">
                            {% if link %}
                                <a href="{{ link }}">{{ body|replace({"<p>" : "","</p>" : "</br>"})|truncate(200, true, true)|raw }}</a>
                            {% else %}
                                {{ body|replace({"<p>" : "","</p>" : "</br>"})|truncate(200, true, true)|raw }}
                            {% endif %}
                        </p>
                    {% endif %}
                </columns>
            {% endif %}
        </row>
        <spacer size="20"></spacer>
    {% endfor %}
    {% endraw %}
</container>
