{% raw %}
    <container class="section-item">
        <table role="presentation" class="quick-access-table row">
            <tr>
                <?php
                    $last_element = count( $args['block_links'] ) - 1;
                    foreach ( $args['block_links'] as $i => $block_qa ) :
                    if ( empty ( $i ) ) : ?>
                        <th class="small-12 large-2 columns first quick-access-item" valign="top">
                            <a href="<?php echo esc_url( $block_qa['link'] ) ?>">
                                <center>
                                    <img src="<?php echo esc_attr( $block_qa['icon'] ) ?>" alt="">
                                </center>
                                <spacer size="5"></spacer>
                            </a>
                            <a href="<?php echo esc_url( $block_qa['link'] ) ?>">
                                <center>
                                    <span><font style="color: #ffffff;"><?php echo esc_html( $block_qa['title'] ); ?></font></span>
                                </center>
                            </a>
                        </th>
                        <th style="width: 8px;" class="quick-access-spacer"></th>
                    <?php elseif ( $i == $last_element ) : ?>
                        <th class="small-12 large-2 columns last quick-access-item" valign="top">
                            <a href="<?php echo esc_url( $block_qa['link'] ) ?>">
                                <center>
                                    <img src="<?php echo esc_attr( $block_qa['icon'] ) ?>" alt="">
                                </center>
                            </a>
                                <spacer size="5"></spacer>
                                <a href="<?php echo esc_url( $block_qa['link'] ) ?>">
                                    <center>
                                        <span><font style="color: #ffffff;"><?php echo esc_html( $block_qa['title'] ); ?></font></span>
                                    </center>
                                </a>
                        </th>
                    <?php else : ?>
                        <th class="small-12 large-2 columns quick-access-item" valign="top">
                            <a href="<?php echo esc_url( $block_qa['link'] ) ?>">
                                <center>
                                    <img src="<?php echo esc_attr( $block_qa['icon'] ) ?>" alt="">
                                </center>
                                <spacer size="5"></spacer>
                            </a>
                            <a href="<?php echo $block_qa['link'] ?>">
                                <center>
                                    <span><font style="color: #ffffff;"><?php echo esc_html( $block_qa['title'] ); ?></font></span>
                                </center>
                            </a>
                        </th>
                        <th style="width: 8px;" class="quick-access-spacer"></th>
                    <?php endif ?>
                <?php endforeach ?>
            </tr>
        </table>
        <spacer size="90"></spacer>
    </container>
{% endraw %}
