// Global section styles
.section {
    .container {
        background: none;
    }

    .sr-only {
        font-size: 0 !important;
    }

    ul {
        list-style: none !important;
        list-style-type: none !important;
        padding: 0 !important;
        margin: 0 !important;
    }

    // Main title for the section
    &-title {
        tr > td {
            padding-bottom: 19px;
            padding-top: 70px;

            @media only screen and (max-width: #{$global-breakpoint}) {
                padding-top: 50px !important;
            }
        }

        h2 {
            color: $color-black;
            font-size: 35px;
            line-height: 1;
            margin-bottom: 0;
            Margin-bottom: 0;
            text-align: left;
        }

        hr {
            background-color: $color-1--2;
            border-bottom: 1px solid $color-1--2;
            border-left: 1px solid $color-1--2;
            border-right: 1px solid $color-1--2;
            border-top: 8px solid $color-1--2;
            color: $color-1--2;
            height: 0 !important;
            margin: 9px 0 0;
        }
    }

    // Single item (card) wrapper in section
    &-item {
        margin: 0 auto $column-padding-bottom;
        Margin: 0 auto $column-padding-bottom;
    }

    // Bottom button in section
    &-button table.button {
        table td {
            letter-spacing: 1.2px;

            @media only screen and (max-width: #{$global-breakpoint}) {
                display: block !important;
                text-align: center !important;
                width: auto !important;
            }

            a {
                letter-spacing: 1.2px;

                @media only screen and (max-width: #{$global-breakpoint}) {
                    text-align: center !important;
                }
            }

            &.events-btn {
                background: none !important;
                border: 1px solid $color-white !important;

                @media only screen and (max-width: #{$global-breakpoint}) {
                    margin-bottom: 15px !important;
                    Margin-bottom: 15px !important;
                }
            }

            &.events-btn + td:not(.button-wrap) {
                border: none !important;
                background: none !important;

                @media only screen and (max-width: #{$global-breakpoint}) {
                    display: none !important;
                }
            }
        }
    }

    // Single item (card) image in section
    //&-image {}

    // Single item (card) content in section
    //&-content {}

    th.columns table.category {
        margin-bottom: 0;
        Margin-bottom: 0;
        width: auto;

        td {
            background-color: $color-1--2;
            color: $color-white;
            font-size: 11px;
            font-weight: 700;
            padding: {
                bottom: 3px;
                left: 14px;
                right: 14px;
                top: 3px;
            };
            text-align: center;
            text-transform: uppercase;

            a {
                color: $color-white;
            }
        }
    }

    span.section__subtitle,
    p.section__subtitle {
        margin-bottom: 0;
        Margin-bottom: 0;

        @include with-link {
            color: $color-1--1;
            font-size: 14px !important;
            font-weight: 400 !important;
            letter-spacing: 0 !important;
            line-height: (18/16) !important;
            margin-bottom: 0;
            Margin-bottom: 0;
            text-transform: uppercase !important;
        }
    }

    &__title {
        margin: 0 !important;
        Margin: 0 !important;

        @include with-link {
            color: $header-color;
            font-size: 22px;
            font-weight: 700;
            line-height: 1.13;
        }
    }

    &__teaser {
        font-size: 16px;
        line-height: (18/16);
        margin-bottom: 0;
        Margin-bottom: 0;

        a {
            display: block;
            line-height: 1.5;
        }
    }
}

td {
    hyphens: unset !important;
    word-wrap: unset !important;
}

td.expander, th.expander {
    display: none !important;
}

.propose {
    color: $color-3--3;
    font-size: 13px;
    font-weight: 700;
    text-align: right;
    text-transform: uppercase;

    &:visited {
        color: $color-3--3;
    }

    img {
        display: inline;
    }
}

// Change heading border color
.standard {
    .wrapper-inner {
        padding-bottom: 30px;

        @media only screen and (max-width: #{$global-breakpoint}) {
            padding-bottom: 10px !important;
        }
    }

    .section-image.large-6 {
        padding-left: 8px;

        @media only screen and (max-width: #{$global-breakpoint}) {
            padding-bottom: 40px !important;
        }
    }

    .section-content.large-6 {
        padding-right: 8px;

        @media only screen and (max-width: #{$global-breakpoint}) {
            padding-bottom: 16px !important;
        }
    }

    .section-content {
        padding-bottom: 40px;
    }

    .section__title {
        @include with-link {
            font-size: 22px;
            line-height: (24/22);
        }
    }

    .section__teaser {
        @include with-link {
            color: $color-3--3;
            font-size: 18px;
            line-height: (21/18);
        }
    }
}

// Edito
.section.edito {
    background: #f7f7f7;
    width: 100%;

    .wrapper-inner {
        padding-bottom: 54px;

        @media only screen and (max-width: #{$global-breakpoint}) {
            padding-bottom: 34px !important;
        }
    }

    .section-content {
        @media only screen and (max-width: #{$global-breakpoint}) {
            padding-bottom: 37px !important;
        }
    }

    .edito__name {
        p {
            margin-bottom: 0;
            Margin-bottom: 0;

            @include with-link {
                color: $color-3--3;
                font-size: 14px;
                font-weight: 400;
                line-height: (16/14);
                text-align: center;
            }

            strong {
                font-size: 16px;
                line-height: (18/16);
            }
        }
    }

    .section__title {
        @media only screen and (max-width: #{$global-breakpoint}) {
            text-align: left !important;
        }

        @include with-link {
            color: $color-black;
            font-size: 25px;
            line-height: (29/25);
        }
    }

    .section__teaser {
        @include with-link {
            color: $color-black;
            font-size: 20px;
            line-height: (30/20);
        }
    }
}

// Focus
.section.focus {
    .wrapper-inner {
        padding-bottom: 54px;

        @media only screen and (max-width: #{$global-breakpoint}) {
            padding-bottom: 34px !important;
        }
    }

    .section-image {
        padding-bottom: 30px;
    }

    .section-content th {
        padding-left: 106px;
        padding-right: 106px;

        @media only screen and (max-width: #{$global-breakpoint}) {
            padding-bottom: 0 !important;
            padding-left: 0 !important;
            padding-right: 0 !important;
            padding-top: 0 !important;
        }
    }

    .section__title {
        @include with-link {
            font-size: 30px;
            line-height: (32/30);
        }
    }

    .section__teaser {
       @include with-link {
           color: $color-3--3;
           font-size: 18px;
           line-height: (21/18);
       }
    }

    th.columns table.focus__badge {
        width: auto;

        td {
            background-color: $color-1--2;
            color: $color-white;
            font-size: 23px;
            font-weight: 700;
            padding-bottom: 12px;
            padding-left: 24px;
            padding-right: 24px;
            padding-top: 12px;
        }
    }
}

// News
.section.news {
    background: $color-white;

    .section-title {
        tr > td {
            padding-bottom: 20px;
            padding-top: 42px;

            @media only screen and (max-width: #{$global-breakpoint}) {
                padding-bottom: 10px !important;
            }
        }
    }

    .columns.large-4,
    .columns.large-8 {
        padding-bottom: 20px;
    }

    .section-content {
        padding-left: 9px;
        padding-top: 20px;

        @media only screen and (max-width: #{$global-breakpoint}) {
            padding-bottom: 30px !important;
            padding-top: 0 !important;

            .spacer {
                display: none !important;
            }
        }
    }

    .section-image {
        min-width: 196px;

        @media only screen and (max-width: #{$global-breakpoint}) {
            padding-bottom: 15px !important;
            padding-right: 0 !important;
            min-width: 108px !important;
        }
    }

    th.first.section-image img {

        @media only screen and (max-width: #{$global-breakpoint}) {
            width: 108px !important;
            height: 72px !important;
        }
    }

    span.section__subtitle {
        margin-bottom: 0;
        Margin-bottom: 0;

        @include with-link {
            color: $color-1--1;
            font-size: 14px;
            font-weight: 400;
            letter-spacing: 2.88px;
            line-height: (16/14);

            @media only screen and (max-width: #{$global-breakpoint}) {
                font-size: 12px !important;
                display: block !important;
            }
        }

        @media only screen and (max-width: #{$global-breakpoint}) {
            margin-bottom: 5px !important;
            Margin-bottom: 5px !important;
        }
    }

    .section__title {
        @include with-link {
            font-size: 22px;
            line-height: (24/22);

            @media only screen and (max-width: #{$global-breakpoint}) {
                font-size: 20px !important;
            }
        }
    }

    .news-focus {
        .columns {
            padding-bottom: 50px;
        }

        &__image {
            @media only screen and (max-width: #{$global-breakpoint}) {
                padding-bottom: 16px !important;
            }
        }

        &__content {
            padding-top: 30px;

            @media only screen and (max-width: #{$global-breakpoint}) {
                padding-bottom: 40px !important;
                padding-top: 0 !important;
            }

            span.section__subtitle {
                @include with-link {
                    color: $color-1--1;
                    font-size: 16px;
                    font-weight: 400;
                    letter-spacing: 2.88px;
                    line-height: (18/16);
                    margin-bottom: 0;
                    Margin-bottom: 0;
                    text-transform: uppercase;

                    @media only screen and (max-width: #{$global-breakpoint}) {
                        font-size: 15px !important;
                    }
                }
            }

            .section__title {
                @include with-link {
                    font-size: 30px;
                    font-weight: 700;
                    line-height: (32/30);

                    @media only screen and (max-width: #{$global-breakpoint}) {
                        font-size: 30px !important;
                    }
                }
            }

            .section__teaser {
                @include with-link {
                    font-size: 18px;
                }
            }
        }
    }

    .section-button-column {
        padding-bottom: 54px;
        padding-top: 50px;

        @media only screen and (max-width: #{$global-breakpoint}) {
            padding-bottom: 34px !important;
            padding-top: 20px !important;
        }
    }
}

// Events
.section.events {
    background-color: $color-1--2;
    width: 100%;

    hr {
        background-color: $color-white;
        border-bottom: 1px solid $color-white;
        border-left: 1px solid $color-white;
        border-right: 1px solid $color-white;
        border-top: 8px solid $color-white;
        color: $color-white;
        height: 0 !important;
        margin: 9px 0 0;

        @media only screen and (max-width: #{$global-breakpoint}) {
            margin: 9px auto 0 !important;
        }
    }

    .section-title {
        h2 {
            color: $color-white;

            @media only screen and (max-width: #{$global-breakpoint}) {
                text-align: center !important;
            }
        }

        hr {
            @media only screen and (max-width: #{$global-breakpoint}) {
                margin: 9px auto 0 !important;
                text-align: center !important;
            }
        }
    }

    ul {
        list-style: none !important;
        padding: 0 !important;
        margin: 0 !important;
    }

    .section-image.columns {
        @media only screen and (max-width: #{$global-breakpoint}) {
            padding-bottom: 0 !important;
        }
    }

    .events-image {
        min-width: 196px;

        img {
            height: auto;
            width: 100%;

            @media only screen and (max-width: #{$global-breakpoint}) {
                padding-bottom: 10px !important;
                width: 150px !important;
            }
        }
    }

    a {
        display: block;
    }

    .date-wrap {
        padding-left: 12px;
        padding-right: 12px;
    }

    .section__title {
        @include with-link {
            color: $color-white;
            font-size: 22px;
            line-height: (24/22);

            @media only screen and (max-width: #{$global-breakpoint}) {
                font-size: 18px !important;
            }
        }

        .date {
            color: $color-white;
            font-size: 14px;
            font-weight: 700;
            line-height: 1;
            margin-bottom: 0;
            Margin-bottom: 0;
            padding-bottom: 0;
            padding-top: 0;

            img {
                display: inline;
                min-width: 14px;
                vertical-align: bottom;
                width: 14px;
            }

            a {
                color: $color-white;
                font-size: 14px;
                font-weight: 700;
                line-height: 1;

                @media only screen and (max-width: #{$global-breakpoint}) {
                    font-size: 14px !important;
                }
            }
        }

        .events-date {
            background-color: $color-1--1;
            min-width: 237px;
            padding-bottom: 0;
            padding-left: 0;
            padding-right: 0;
            padding-top: 0;
            width: auto;
        }
    }

    .section__teaser {
        max-width: 244px;

        @media only screen and (max-width: #{$global-breakpoint}) {
            max-width: 100% !important;
        }

        @include with-link {
            color: $color-2--1;
            font-size: 12px;
            font-weight: 400;
            line-height: (14/12);
        }
    }

    .events__timeplace {
        td {
            color: $color-1--3;
            font-size: 11px;
            line-height: 1.2;
            padding-bottom: 1px;
            padding-top: 1px;
            vertical-align: top;

            a {
                color: $color-white;
            }

        }

        .events__icon {
            height: 12px;
            width: 12px;
            padding-right: 3px;

            img {
                margin: 0 auto;
                Margin: 0 auto;
            }
        }
    }

    .events-focus {
        .columns.large-6 {
            padding-bottom: 50px;
        }

        .events-image.columns {
            @media only screen and (max-width: #{$global-breakpoint}) {
                padding-bottom: 0 !important;

                img {
                    width: 100% !important;

                    @media only screen and (max-width: #{$global-breakpoint}) {
                        padding-bottom: 14px !important;
                    }
                }
            }
        }

        .section__title {
            @include with-link {
                font-size: 30px;
                line-height: (32/30);

                @media only screen and (max-width: #{$global-breakpoint}) {
                    font-size: 30px !important;
                }
            }

            .date {
                color: $color-white;
                font-size: 14px;
                font-weight: 700;
                line-height: 1;
                margin-bottom: 0;
                Margin-bottom: 0;
                padding-bottom: 0;
                padding-top: 0;

                img {
                    display: inline;
                    min-width: 14px;
                    vertical-align: bottom;
                    width: 14px;
                }

                a {
                    color: $color-white;
                    font-size: 14px;
                    font-weight: 700;
                    line-height: 1;

                    @media only screen and (max-width: #{$global-breakpoint}) {
                        font-size: 14px !important;
                    }
                }
            }

            .events-date {
                background-color: $color-1--1;
                min-width: 237px;
                padding-bottom: 0;
                padding-left: 0;
                padding-right: 0;
                padding-top: 0;
                width: auto;
            }
        }
    }

    .events-item {
        .columns {
            padding-bottom: 20px;
        }

        .divider {
            display: block;
        }
    }

    .banner-wrap {
        padding-bottom: 70px;
        padding-left: 16px;
        padding-right: 16px;

        @media only screen and (max-width: #{$global-breakpoint}) {
            padding-bottom: 16px !important;
        }
    }

    .banner-img {
        min-width: 310px;

        @media only screen and (max-width: #{$global-breakpoint}) {
            display: block !important;
            min-width: auto !important;
            width: auto !important;

            img {
                width: 100% !important;
            }
        }
    }

    .banner-content {
        background-color: $color-3--5;
        padding: {
            //bottom: 28px;
            left: 42px;
            right: 24px;
            //top: 28px;
        };

        @media only screen and (max-width: #{$global-breakpoint}) {
            display: block !important;
            width: auto !important;
        }

        h3 {
            margin-bottom: 0;
            Margin-bottom: 0;

            @include with-link {
                font-size: 24px;
                font-weight: 700;
                line-height: (26/24);
            }
        }

        td {
            vertical-align: bottom;

            img {
                display: inline;
                min-width: 15px;
                vertical-align: bottom;
                width: 15px;
            }
        }
    }

    .section-button-column {
        padding-bottom: 54px;
        padding-top: 30px;

        @media only screen and (max-width: #{$global-breakpoint}) {
            padding-bottom: 34px !important;
        }
    }

    .button-wrap {
        background: $color-1--1 !important;
        border: 1px solid $color-1--1 !important;
    }
}

// Quick Access
.section.quick-access {
    background-color: $color-3--6;
    width: 100%;

    @media only screen and (max-width: #{$global-breakpoint}) {
        background-color: $color-white !important;
    }

    th {
        @media only screen and (max-width: #{$global-breakpoint}) {
            padding-top: 0 !important;
        }
    }

    .wrapper-inner {
        padding-bottom: 56px;

        @media only screen and (max-width: #{$global-breakpoint}) {
            padding-bottom: 27px !important;
        }
    }

    hr {
        background-color: $color-1--2;
        color: $color-1--2;

        @media only screen and (max-width: #{$global-breakpoint}) {
            margin-left: 34px !important;
        }
    }

    hr.decor-haut {
        @media only screen and (max-width: #{$global-breakpoint}) {
            display: none !important;
        }
    }

    h2.section__subtitle {
        margin-bottom: 0;
        Margin-bottom: 0;

        @include with-link {
            color: $color-1--1;
            font-size: 14px;
            font-weight: 400;
            letter-spacing: 3.88px;
            line-height: (18/16);
            margin-bottom: 0;
            Margin-bottom: 0;
            text-transform: uppercase;
        }
    }

    table.section-item > tbody > tr >td {
        padding-bottom: 0;
        padding-top: 0;

        @media only screen and (max-width: #{$global-breakpoint}) {
            padding-left: 10px !important;
            padding-right: 10px !important;
        }
    }

    .section-title {
        tr > td {
            padding-bottom: 2px;
            padding-top: 47px;

            @media only screen and (max-width: #{$global-breakpoint}) {
                padding-bottom: 4px !important;
                padding-top: 50px !important;
            }
        }

        .section__subtitle {
            text-align: center;

            @media only screen and (max-width: #{$global-breakpoint}) {
                padding-bottom: 4px !important;
            }
        }
    }

    .quick-access-table {
        border-collapse: separate;
        border-spacing: 0 !important;
        margin: 0 auto;
        table-layout: auto;
        width: auto;

        @media only screen and (max-width: #{$global-breakpoint}) {
            display: block !important;
        }

        tr, tbody {
            @media only screen and (max-width: #{$global-breakpoint}) {
                display: block !important;
            }
        }
    }

    .quick-access-item {
        color: $color-1--2;
        display: inline-block;
        font-size: 16px;
        font-weight: 700 !important;
        line-height: 1.2;
        margin: 0 !important;
        Margin: 0 !important;
        padding-bottom: 20px;
        padding-left: 27px;
        padding-right: 27px;
        padding-top: 42px;
        vertical-align: top !important;
        text-transform: uppercase;

        @media only screen and (max-width: #{$global-breakpoint}) {
            box-sizing: border-box;
            height: auto !important;
            font-size: 14px;
            padding-bottom: 13px !important;
            padding-left: 12px !important;
            padding-right: 12px !important;
            padding-top: 0 !important;
            width: 50%;
        }

        a {
            font-size: 16px;
            font-weight: 700;
            display: block;
            line-height: 18px;

            @media only screen and (max-width: #{$global-breakpoint}) {
                font-size: 14px;
            }
        }

        .title-quick {
            @media only screen and (max-width: #{$global-breakpoint}) {
                display: inline-block !important;
                padding-left: 4px !important;
                vertical-align: middle !important;
            }
        }

        font {
            display: block;
        }

        .quick-access-spacer {
            border-spacing: 0 !important;
        }

        .spacer {
            @media only screen and (max-width: #{$global-breakpoint}) {
                display: none !important;
            }
        }

        &__image {
            display: inline-block;
            width: 25px;
        }

        img {
            height: 25px;
            margin: 0;
            Margin: 0;
            width: auto;

            @media only screen and (max-width: #{$global-breakpoint}) {
                display: inline-block !important;
                vertical-align: middle !important;
            }
        }
    }
}

// Albums
.section.albums {
    .wrapper-inner {
        padding-bottom: 54px;

        @media only screen and (max-width: #{$global-breakpoint}) {
            padding-bottom: 34px;
        }
    }

    .section__subtitle {
        @include with-link {
            font-size: 16px !important;
        }
    }

    .section-image {
        padding-bottom: 30px;
    }

    .section-content th {
        @media only screen and (max-width: #{$global-breakpoint}) {
            padding-bottom: 0 !important;
            padding-left: 0 !important;
            padding-right: 0 !important;
            padding-top: 0 !important;
        }

    }

    .section__title {
        @include with-link {
            font-size: 30px;
            line-height: (32/30);
        }
    }

    .section__teaser {
        @include with-link {
            color: $color-3--3;
            font-size: 18px;
            line-height: (21/18);
        }
    }

    .albums__counter {
        @include with-link {
            color: $color-3--3;
            font-size: 14px;
            font-weight: 400;
            line-height: (16/14);
        }
    }

    .section-button-column {
        padding-bottom: 0;
        padding-top: 34px;

        @media only screen and (max-width: #{$global-breakpoint}) {
            padding-bottom: 0 !important;
        }
    }
}

// Publications
.section.publications {
    .section-image {
        padding-right: 7.5px;

        a {
            border: 1px solid $color-3--2;
            display: block;

            @media only screen and (max-width: #{$global-breakpoint}) {
                margin-left: 0 !important;
                margin-right: 0 !important;
                width: 196px !important;
            }
        }
    }

    .section-content {
        padding-left: 7.5px;
    }

    &.button-wrap {
        background: $color-1--2 !important;
        border: 1px solid $color-1--2 !important;
    }

    .section__publication-wrap {
        color: $color-3--3;
        font-size: 12px;
        margin-top: -17px;
        padding-left: 55px;
        text-transform: uppercase;

        @media only screen and (max-width: #{$global-breakpoint}) {
            margin-top: 0 !important;
            padding-left: 0 !important;
            text-align: left !important;
        }
    }

    .section__publication-number {
        display: block;
        font-weight: 700;
    }

    .section__publication-document {
        hr {
            background-color: $color-3--3;
            border-bottom: 1px solid $color-3--3;
            border-left: 1px solid $color-3--3;
            border-right: 1px solid $color-3--3;
            border-top: 4px solid $color-3--3;
            color: $color-3--3;
            height: 0 !important;
            margin-right: 10px !important;
            margin-top: 10px !important;

            @media only screen and (max-width: #{$global-breakpoint}) {
                display: none;
            }
        }
    }

    .section__subtitle {
        @include with-link {
            font-size: 16px !important;
        }
    }

    .section__subtitle,
    .section__title,
    .section__publication,
    .section__teaser {
        @include with-link {
            @media only screen and (max-width: #{$global-breakpoint}) {
                text-align: left !important;
            }
        }
    }

    .section__title {
        @include with-link {
            font-size: 30px;
            line-height: (32/30);
        }
    }

    .section__publication {
        @media only screen and (max-width: #{$global-breakpoint}) {
            margin-bottom: 0 !important;
        }

        @include with-link {
            font-size: 25px;
            line-height: 1;
        }
    }

    .section__teaser {
        @include with-link {
            color: $color-3--3;
            font-size: 18px;
            font-weight: 400;
            line-height: (21/18);
        }
    }

    .size {
        color: $color-3--4;
    }

    .publications__links {
        @media only screen and (max-width: #{$global-breakpoint}) {
            width: auto !important;

            tbody tr {
                text-align: center !important;
                width: auto !important;
            }
        }

        td {
            vertical-align: middle;

            center {
                min-width: 0 !important;
            }
        }
    }

    .publications__text {
        border: 1px solid $color-3--3;
        display: inline-block;
        margin-bottom: 5px;
        Margin-bottom: 5px;
        padding: {
            bottom: 12px;
            left: 28px;
            right: 27px;
            top: 12px;
        };

        @media only screen and (max-width: #{$global-breakpoint}) {
            padding-bottom: 11px !important;
            padding-top: 11px !important;
        }

        @include with-link {
            color: $color-3--3;
            display: inline-block;
            font-size: 12px;
            letter-spacing: 1.22px;
            margin-bottom: 5px;
            Margin-bottom: 5px;
            text-transform: uppercase;
        }

        center {
            min-width: 0 !important;
        }

        img {
            display: inline;
            vertical-align: middle;
        }
    }

    .section-button-column {
        padding-bottom: 54px;
        padding-top: 14px;

        @media only screen and (max-width: #{$global-breakpoint}) {
            padding-bottom: 34px !important;
        }
    }
}
