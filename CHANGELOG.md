# Changelog

* [2.1.1 (2019-01-12)] [TASK] Implement stylelint
* [2.1.0 (2018-06-18)] [TASK] Changelist:
  - Implement new custom feature for search/replace in rendered code. z-var. Documentation in README.md;
  - Redesign sections according to latest NL designs.
* [2.0.6 (2018-06-18)] [TASK] Changelist:
  - Gulpfile add missing round bracket;
  - Bump gulp-inline-css version;
  - use a new way to include google-fonts;
  - change default text format for lorem-ipsum plugin.
* [2.0.5 (2018-06-18)] [TASK] Gulpfile code refactoring. Improve Nunjucks custom filters.
* [2.0.4 (2018-06-18)] [TASK] Gulpfile code refactoring. Improve errorHandler function.
* [2.0.3 (2018-03-19)] [TASK] Gulpfile code refactoring. Improve watching function, use arrow functions.
* [2.0.2 (2018-03-14)] [TASK] Improve variables, make them easier to understand. Update Readme, add few instructions.
* [2.0.1 (2018-03-07)] [TASK] Update dependencies, improve inlining process. Add export-watch task.
* [2.0.0 (2018-03-05)] [INTRODUCE] Braking changes. New modules is required. new version.
* [1.2.1 (2018-01-30)] [TASK] Optimize code according to Litmus tests.
* [1.2 (2018-01-30)] [TASK] Add separated templates in render scope for easier implementation process on the back-end
* [1.1.1 (2017-11-15)] [FIX] Fix macro context issue. Remove build folder from repo.
* [1.1 (2017-11-10)] [TASK] Add additional npm tasks and flags. Update starter according to Jenkis build script.
* [1.0.1 (2017-10-26)] [TASK] Add build task (without browsersync), update README
* [1.0.0 (2017-09-18)] [TASK] Write starter template from scratch using Foundation emails, Inky and Nunjuks.
