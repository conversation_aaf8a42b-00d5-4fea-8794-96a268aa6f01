{% import "helpers/mixins.njk" as mixins with context -%}

{{ mixins.includeCss(isTemplateIncluded) }}
{% set edito = {
    title: "{title}||{{ title_block }}||<?php echo esc_html( $args['block_title'] ); ?>||Éditorial",
    itemImage: "{picto}||{{ image }}||<?php echo esc_url( $args['block_image'] ); ?>||{{ imgPath }}edito.png",
    itemName: "{author}||{{ name }}||<?php echo esc_html( $args['block_caption_name'] ); ?>||<PERSON><PERSON> Jean DUPONT",
    itemPost: "{function -> f:format.raw()}||{{ function }}||<?php echo nl2br( $args['block_caption_function'] ); ?>||Adjoint au maire</br>Titre de la fonction",
    itemTitle: "{title1}||{{ title }}||<?php echo esc_html( $args['block_edito_title'] ); ?>||Lorem ipsum dolor sit amet, consetetur",
    itemText: "{text -> f:format.nl2br() -> f:format.raw()}||{{ teaser|nl2br|truncate(200, true, true)|raw }}||<?php echo nl2br( $args['block_edito_text'] ); ?>||Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.",
    itemLink: "{link}||{{ link }}||<?php echo esc_url( $args['block_link'] ); ?>||#"
} | parseBE -%}
<center>
    <wrapper class="section edito">
        {{- mixins.sectionTitle(edito.title) -}}
        <container class="section-item">
            <row>
                <columns large="8" class="first section-content">
                    <h3 class="section__title small-text-center">
                        {% if isTYPO3 %}{% raw %}<f:if condition="{link}"><f:then>{% endraw %}{% endif %}
                        {% if isDRUPAL %}{% raw %}{% if link %}{% endraw %}{% endif %}
                        {% if isWORDPRESS %}{% raw %}<?php if ( ! empty( $args['block_link'] ) ): ?>{% endraw %}{% endif %}
                            <a href="{{ edito.itemLink }}">{{ edito.itemTitle }}</a>
                        {% if isTYPO3 %}{% raw %}</f:if>{% endraw %}{% endif %}
                        {% if isDRUPAL %}{% raw %}{% endif %}{% endraw %}{% endif %}
                        {% if isWORDPRESS %}{% raw %}<?php endif; ?>{% endraw %}{% endif %}
                    </h3>
                    <spacer size="23"></spacer>
                    {% if isDRUPAL %}{% raw %}{% if teaser %}{% endraw %}{% endif %}
                    {% if isWORDPRESS %}{% raw %}<?php if ( ! empty( $args['block_image'] ) ): ?>{% endraw %}{% endif %}
                        <p class="section__teaser small-text-center"><a href="#" tabindex="-1" role="presentation">{{ edito.itemText }}</a></p>
                    {% if isDRUPAL %}{% raw %}{% endif %}{% endraw %}{% endif %}
                    {% if isWORDPRESS %}{% raw %}<?php endif; ?>{% endraw %}{% endif %}
                </columns>
                {% if isDRUPAL %}{% raw %}{% if image %}{% endraw %}{% endif %}
                {%- if isWORDPRESS -%}{% raw %}<?php if ( ! empty( $args['block_image'] ) ): ?>{% endraw %}{%- endif -%}
                <columns large="4" class="last section-image">
                    {% if isTYPO3 %}{% raw %}<f:if condition="{link}"><f:then>{% endraw %}{% endif %}
                            {% if isDRUPAL %}{% raw %}{% if link %}{% endraw %}{% endif %}
                            {% if isWORDPRESS %}{% raw %}<?php if ( ! empty( $args['block_link'] ) ): ?>{% endraw %}{% endif %}
                            {% if isTYPO3 %}{% raw %}<f:if condition="{picto}&&{link}"><f:then>{% endraw %}{% endif %}
                                    <a href="{{ edito.itemLink }}" tabindex="-1" aria-hidden="true">
                                        <img class="small-float-center" src="{{ edito.itemImage }}" alt="">
                                    </a>
                                {% if isTYPO3 %}{% raw %}</f:if>{% endraw %}{% endif %}
                            <div class="edito__name">
                                <spacer size="13"></spacer>
                                <p class="small-text-center">
                                    <a href="#" tabindex="-1" role="presentation">
                                        <strong>{{ edito.itemName }}</strong>
                                    </a>
                                </p>
                                <spacer size="10"></spacer>
                                <p class="small-text-center">
                                    <a href="#" tabindex="-1" role="presentation">{{ edito.itemPost }}</a>
                                </p>
                            </div>
                        {% if isTYPO3 %}{% raw %}</f:if>{% endraw %}{% endif %}
                    {% if isDRUPAL %}{% raw %}{% endif %}{% endraw %}{% endif %}
                    {% if isWORDPRESS %}{% raw %}<?php endif; ?>{% endraw %}{% endif %}
                </columns>
                {% if isDRUPAL %}{% raw %}{% endif %}{% endraw %}{% endif %}
                {%- if isWORDPRESS -%}{% raw %}<?php endif; ?>{% endraw %}{%- endif -%}
            </row>
{#            <spacer size="54"></spacer>#}
        </container>
    </wrapper>
</center>
