{% import "helpers/mixins.njk" as mixins with context -%}
{{ mixins.includeCss(isTemplateIncluded) }}
{% set focus = {
    title: '{title}||{{ title_block }}||<?php echo esc_html( $args[\'block_title\'] ); ?>||Focus',
    itemLink: '{link}||{{ link }}||<?php ?>||#',
    itemSubtitle: '{subtitle}||{{ subtitle }}||<?php ?>||{{ lorem(range(1, 3) | random, "words") | capitalize }}',
    itemTitle: '{pageTitle}||{{ title }}||<?php ?>||Lorem ipsum dolor sit amet, consetetur ',
    itemText: '{description -> f:format.nl2br()}||{{ teaser|nl2br|truncate(200, true, true)|raw }}||<?php ?>||Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet.',
    itemPicture: '{image}||{{ image }}||<?php ?>||https://picsum.photos/620/413',
    itemAlt: '{imageAlt}||{{ image_alt }}||<?php ?>||{{ lorem(1) }}'
} | parseBE -%}

<center>
    <wrapper class="section focus">
        {{- mixins.sectionTitle(focus.title) -}}
        <container class="section-item">
            {% if isTYPO3 or isWORDPRESS %}
                {% include "items/{{ env }}/itemFocus.njk" | interpolate -%}
            {%  else %}
                {% if isDRUPAL %}{% raw %}{% if image %}{% endraw %}{% endif %}
                <row>
                    <columns class="first last section-image">
                        <center>
                            <a href="{{ focus.itemLink }}" tabindex="-1" aria-hidden="true">
                                <img src="{{ focus.itemPicture }}" alt="">
                            </a>
                        </center>
                    </columns>
                </row>
                {% if isDRUPAL %}{% raw %}{% endif %}{% endraw %}{% endif %}
                <row>
                    <th class="first last section-content small-12 large-12 columns">
                        <table role="presentation">
                            <tbody>
                                <tr>
                                    <th>
                                        <h3 class="section__title">
                                            <span class="section__subtitle">
                                                <a href="{{ focus.itemLink }}" tabindex="-1" role="presentation">{{ focus.itemSubtitle }}</a>
                                            </span>
                                        <spacer size="15"></spacer>
                                            <a href="{{ focus.itemLink }}">{{ focus.itemTitle }}</a>
                                        </h3>
                                        <spacer size="20"></spacer>
                                        {% if isDRUPAL %}{% raw %}{% if teaser %}{% endraw %}{% endif %}
                                        <p class="section__teaser">
                                            <a href="{{ focus.itemLink }}"tabindex="-1" role="presentation">{{ focus.itemText }}</a>
                                        </p>
                                        {% if isDRUPAL %}{% raw %}{% endif %}{% endraw %}{% endif %}
                                    </th>
                                </tr>
                            </tbody>
                        </table>
                    </th>
                </row>
            {% endif %}
        </container>
    </wrapper>
</center>
