{% raw %}
    <f:if condition="{news}">
        <container>
            <f:for each="{news}" as="newsItem">
                <row class="events-item">
                    {n:link(newsItem:newsItem,settings:settings,uriOnly:'1') -> v:variable.set(name:'relativeLinkToSingle')}
                    {f:uri.typolink(parameter:relativeLinkToSingle,absolute:1) -> v:variable.set(name:'linkToSingle')}
                    <columns large="6" class="first">
                        <table role="presentation">
                            <tr>
                                <f:if condition="{newsItem.mainMedia} || {newsItem.falMedia}">
                                    <td class="events-image">
                                        <a href="{linkToSingle}" aria-hidden="true" tabindex="-1">
                                            <img src="{f:render(partial:'Common/ListImageNewsletter', section:'MainOrFalImage', arguments:'{newsItem:newsItem,width:\'220c\',height:\'220c\'}') -> v:format.trim()}" alt=""/>
                                        </a>
                                    </td>
                                </f:if>
                                <f:if condition="{newsItem.datetime} || {newsItem.archive}">
                                    <td style="width: 100px;" class="events-date">
                                        <a href="{linkToSingle}">
                                            <table>
                                                <tr>
                                                    <td>
                                                        <spacer size="30"></spacer>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <p class="date date--first"><span>{f:format.date(format:'%d',date:newsItem.datetime)}</span></p>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <p class="date date--first">{f:format.date(format:'%b',date:newsItem.datetime)}</p>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <spacer size="7"></spacer>
                                                    </td>
                                                </tr>
                                                <f:if condition="{newsItem.archive}">
                                                    <tr>
                                                        <td>
                                                            <center>
                                                                <img src="{f:uri.image(absolute:1,src:'{pathToImages}newsletter/icons/angle-down.png')}" class="float-center" align="center" alt="">
                                                            </center>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <spacer size="7"></spacer>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <p class="date"><span>{f:format.date(format:'%d',date:newsItem.archive)}</span></p>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td>
                                                            <p class="date">{f:format.date(format:'%b',date:newsItem.archive)}</p>
                                                        </td>
                                                    </tr>
                                                </f:if>
                                                <tr>
                                                    <td>
                                                        <spacer size="10"></spacer>
                                                    </td>
                                                </tr>
                                            </table>
                                        </a>
                                    </td>
                                </f:if>
                            </tr>
                        </table>
                    </columns>
                    <columns large="6" class="last">
                        <f:if condition="{newsItem.categories}">
                            <table role="presentation" class="category">
                                <tr>
                                    <td>
                                        {v:render.template(file:'EXT:stratis_site/Resources/Private/Partials/Common/InlineList.html',variables:'{records:newsItem.categories, glue:\', \'}')}
                                    </td>
                                </tr>
                            </table>
                            <spacer size="10"></spacer>
                        </f:if>
                        <h3 class="section__title">
                            <a href="{linkToSingle}">{newsItem.title}</a>
                        </h3>
                        <spacer size="15"></spacer>
                        <f:if condition="{newsItem.startHour} || {newsItem.relatedLocations}">
                            <table role="presentation" class="events__timeplace">
                                <f:if condition="{newsItem.startHour}">
                                    <tr>
                                        <td class="events__icon"><img src="{f:uri.image(absolute:1,src:'{pathToImages}newsletter/icons/clock.png')}" alt=""></td>
                                        <td class="events__time">{f:render(partial:'Extensions/news/Common/DisplayDateHour', section:'DisplayHour', arguments:'{newsItem:newsItem, settings:settings}')}</td>
                                    </tr>
                                </f:if>
                                <f:if condition="{newsItem.relatedLocations}">
                                    <tr>
                                        <td class="events__icon"><img src="{f:uri.image(absolute:1,src:'{pathToImages}newsletter/icons/map-marker-alt.png')}" alt=""></td>
                                        <td class="events__place">{f:render(partial:'Extensions/news/Common/DisplayLocations', section:'Locations', arguments:'{newsItem:newsItem, settings:settings}')}</td>
                                    </tr>
                                </f:if>
                            </table>
                        </f:if>
                    </columns>
                </row>
            </f:for>
        </container>
    </f:if>
{% endraw %}
