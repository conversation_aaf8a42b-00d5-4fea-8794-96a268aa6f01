{% import "helpers/mixins.njk" as mixins with context -%}
{{- mixins.includeCss(isTemplateIncluded) -}}
{% set standard = {
    title: '{title}||{{ title_block }}||<?php echo esc_html( $args[\'block_title\'] ); ?>||Infos pratiques'
} | parseBE -%}

<center>
    <wrapper class="section standard">
        {{- mixins.sectionTitle(standard.title) -}}

        {% include "items/{{ env }}/itemStandardContent.njk" | interpolate -%}
    </wrapper>
</center>
