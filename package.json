{"name": "stratis-email-starter-template", "version": "2.2.0", "description": "Starter template for <PERSON><PERSON><PERSON> email newletter.", "repository": "https://code.stratis.fr/typo3-8.7/newsletter", "main": "gulpfile.babel.js", "contributors": [{"name": "<PERSON> @ OSI"}, {"name": "<PERSON> @ OSI"}], "scripts": {"build": "gulp build", "build-noinky": "gulp build --no<PERSON>y", "build-watch": "gulp", "inline": "gulp build --inline", "inline-watch": "gulp --inline", "double-watch": "concurrently \"gulp --p=3010 --silent\" \"gulp --p=3020 --silent --inline --env=fe2 --double\"", "export-typo3": "gulp export --inline --env=typo3", "export-watch-typo3": "gulp export-watch --inline --env=typo3", "export-drupal": "gulp export --inline --env=drupal", "export-drupal-noinky": "gulp export --inline --noinky --env=drupal", "export-watch-drupal": "gulp export-watch --inline --env=drupal", "export-wordpress": "gulp export --inline --env=wordpress", "export-wordpress-noinky": "gulp export --inline --noinky --env=wordpress", "export-watch-wordpress": "gulp export-watch --inline --env=wordpress"}, "license": "MIT", "dependencies": {"foundation-emails": "2.2.1"}, "devDependencies": {"@babel/core": "^7.5.4", "@babel/preset-env": "^7.5.4", "@babel/register": "^7.5.5", "browser-sync": "^2.26.7", "change-case": "^3.1.0", "clean-css": "^4.2.1", "concurrently": "^5.0.2", "eslint": "^6.0.1", "eslint-config-airbnb-base": "^13.2.0", "eslint-plugin-import": "^2.18.0", "gulp": "^4.0.2", "gulp-file": "^0.4.0", "gulp-group-css-media-queries": "^1.2.2", "gulp-if": "^2.0.2", "gulp-imagemin": "^6.0.0", "gulp-inject-string": "git+https://stratis:<EMAIL>/npm_modules/gulp-inject-string.git", "gulp-inline-css": "^3.3.2", "gulp-load-plugins": "^2.0.0", "gulp-notify": "^3.2.0", "gulp-nunjucks-render": "^2.2.3", "gulp-plumber": "^1.2.1", "gulp-rename": "^1.4.0", "gulp-sass": "^4.0.2", "gulp-sourcemaps": "^2.6.5", "inky": "git+https://stratis:-<EMAIL>/npm_modules/inky.git", "lorem-ipsum": "^2.0.3", "merge-stream": "^2.0.0", "open": "^6.4.0", "rimraf": "^2.6.3", "siphon-media-query": "^1.0.0", "stylelint": "^10.1.0", "stylelint-config-sass-guidelines": "^6.0.0", "stylelint-order": "^3.0.0", "stylelint-scss": "^3.9.1", "yargs": "^13.2.4"}}