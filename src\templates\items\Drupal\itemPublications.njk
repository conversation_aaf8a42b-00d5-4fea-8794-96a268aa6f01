<container class="section-item">
    {% raw %}
    {% for chunk in array|batch(1) %}
    <row>
        {% for item in chunk %}

        {% set item = drupal_entity('node', item.target_id, 'newsletter')['#node'] %}

        {% if item %}
            {% set link = item.id() ? path('entity.node.canonical', {'node': item.id()}) : NULL %}
            {% set link = link|path_to_absolute %}
            {% set image = drupal_field('field_media_image', 'node', item.id(), 'newsletter').0['#image_url'] %}
            {% set image_alt = drupal_field('field_media_image', 'node', item.id(), 'newsletter').0['#alt'] %}
            {% set theme = drupal_field('field_categories', 'node', item.id(), 'newsletter') %}
            {% set teaser = item.field_description.0.value|striptags %}

            {% set doc = drupal_field('field_medias', 'node', item.id(), 'newsletter') %}
            {% set file = drupal_field('field_media_file', 'media', doc.0['#media'].id()).0["#file"] %}
            {% set file_url = file_url(file.uri.value) %}
            {% set file_type = file.uri.value|split('.')|last|upper %}
            {% set file_size = file.filesize.value|format_file_size %}
            {% set external_link = doc.0['#media'].field_external_link.0.value.uri %}

            {% if image %}
                <columns large="4" class="first section-image">
                    <a href="{{ link }}">
                        <img src="{{ image }}" {% if image_alt %} alt="{{ image_alt }}" {%else%}alt{%endif%}>
                    </a>
                </columns>
                <columns large="8" class="last section-content">
                    {% if theme.0 %}
                        <table role="presentation" class="category"><tr><td><a href="{{ link }}">{{ theme.0 }}</a></td></tr></table>
                        <spacer size="5"></spacer>
                    {% endif %}
                    <h3 class="section__title"><a href="{{ link }}">{{ item.title.value }}</a></h3>
                    <spacer size="10"></spacer>
                    {% if teaser %}
                        <p class="section__teaser"><a href="{{ link }}">{{ teaser|truncate(150, true, true) }}</a></p>
                    {% endif %}
                    {% if file_url or external_link %}
                        <table role="presentation" class="publications__links">
                            {% if file_url %}
                                <tr>
                                    <td class="publications__icon"><a href="{{ file_url }}"><img src="{{ site_url }}/images/icons/read-download.png" alt></a></td>
                                    <td class="publications__text"><a href="{{ file_url }}">{{ 'Download'|t }} <span>{{ file_type }} {{ file_size ? '- ' ~ file_size }}</span></a></td>
                                </tr>
                            {% endif %}
                            {% if external_link %}
                                <tr>
                                    <td class="publications__icon"><a href="{{ external_link }}"><img src="{{ site_url }}/images/icons/read-view.png" alt></a></td>
                                    <td class="publications__text"><a href="{{ external_link }}">{{ 'Browse'|t }}</a></td>
                                </tr>
                            {% endif %}
                        </table>
                    {% endif %}
                </columns>
            {% else %}
                <columns large="8" class="last section-content">
                    {% if theme.0 %}
                        <table role="presentation" class="category"><tr><td><a href="{{ link }}">{{ theme.0 }}</a></td></tr></table>
                        <spacer size="5"></spacer>
                    {% endif %}
                    <h3 class="section__title"><a href="{{ link }}">{{ item.title.value }}</a></h3>
                    <spacer size="10"></spacer>
                    {% if teaser %}
                        <p class="section__teaser"><a href="{{ link }}">{{ teaser|truncate(150, true, true) }}</a></p>
                    {% endif %}
                    {% if file_url or external_link %}
                        <table role="presentation" class="publications__links">
                            {% if file_url %}
                                <tr>
                                    <td class="publications__icon"><a href="{{ file_url }}"><img src="{{ site_url }}/images/icons/read-download.png" alt></a></td>
                                    <td class="publications__text"><a href="{{ file_url }}">{{ 'Download'|t }} <span>{{ file_type }} {{ file_size ? '- ' ~ file_size }}</span></a></td>
                                </tr>
                            {% endif %}
                            {% if external_link %}
                                <tr>
                                    <td class="publications__icon"><a href="{{ external_link }}"><img src="{{ site_url }}/images/icons/read-view.png" alt></a></td>
                                    <td class="publications__text"><a href="{{ external_link }}">{{ 'Browse'|t }}</a></td>
                                </tr>
                            {% endif %}
                        </table>
                    {% endif %}
                </columns>
            {% endif %}
        {% endif %}
        {% endfor %}
        </row>
        <spacer size="34"></spacer>
        {% endfor %}
    {% endraw %}
</container>

