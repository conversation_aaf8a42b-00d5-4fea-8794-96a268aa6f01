# Stratis Email Template

## This template has a Gulp-powered build system with these features:

- Handlebars HTML templates with [Nunjucks](https://mozilla.github.io/nunjucks/)
- Simplified HTML email syntax with [Inky](http://github.com/zurb/inky)
- Sass compilation
- Image compression
- Built-in BrowserSync server
- Full email inlining process
- Exporting BE-ready templates into __www__ folder

## Installation

To use this template, your computer needs [Node.js](https://nodejs.org/en/) 6 or greater.

Open the folder in your command line, and install the needed dependencies:

```bash
git clone ssh://*******************:2222/__PROJECT_NAME___/newsletter.git
cd newsletter
npm install
```

## Image and compiling paths
- Default destination path for build process is `build/` folder. Images puts in `build/img`. So the correct path for images is `img/`.
- When you run `inline` tasks path is changes. Now it leads to jenkins integration server. So before firing `inline` tasks you need to have instance on jenkins first (ask sysadmin for this).

> **NOTICE FE:** You need to specify `images.inline.jenkins` setting in `config.json` file according to your project. Ask your system administrator for the full path which will be created for integration on Jenkins server.

## Exporting process
### Export paths
Major innovation in second generation of the Stratis NewsLetter starter is implemented BE development process and exporting of BE ready templates to a __www__ folder.

Assume that we have the following folder structure:
- projectname.fr/
  - integration
    - integration-main
  - newsletter
  - www

In this case, the list of default export paths will be as follows:
- `projectname.fr/www/typo3conf/ext/stratis_site/Resources/Private/Partials/Extensions/newsletter/` - newsletter template;
- `projectname.fr/www/typo3conf/ext/stratis_site/Resources/Private/Partials/Page/NewsletterCSS.html` - auto generated file with mobile-only CSS's;
- `projectname.fr/www/typo3conf/ext/stratis_site/Resources/Public/Images/newsletter/` - Static images for newsletter.

> **NOTICE BE:** If you want to change default exporting path, You need to edit a `domain` field in `config.json` file.

### Templating
All variables for a particular template (except loop items) are described in config object in the beginning of the file:
```twig
{% set events = {
    title: '{title}||Agenda',
    footerLink: '{allLink}||#',
    footerText: '{allLinkTitle}||Tout l\'agenda'
} | parseBE -%}
```
__parseBE__ key is a custom nunjucks filter that parses given object and split all values between `||` separator. If we firing build or inline type of tasks it takes right part of the value. If we exporting templates in __www__ folder, it takes left part.

In general templates you need to use only object properties:
```twig
<columns class="event-column-half" large="6">
    <p>{{ events.title }}</p>
</columns>
```

If Be dev needs to use BE special tags in generic template, he should use a special form record. For example:
```twig
{% if isBE %}{% raw %}<f:if condition="{picto}">{% endraw %}{% endif %}
    <img src="{{ edito.itemImage }}" alt="{{ edito.itemAlt }}">
{% if isBE %}{% raw %}</f:if>{% endraw %}{% endif %}
```


If BE dev need to add some custom variable into a style attribute (for example: a left/right padding for a first/last grid item), he should use the following construction: `style="z-var: _variableName_"`. After inlining process Gulp will replace it with `style="_bunch of inlined styles_ {_variableName_}"`.

For loop items we using different templates because of many differences between BE and FE templates. But, for BE devs convenience, FE devs also should use variables inside of the loop (and it does make sense because it's a loop). Item templates placed in __items__ folder. Name pattern is `item{main template name}{Be\Fe}.njk`.
 devs should use


### Sitefactory
You can use `z-var` for sitefactory property declarations, here is an example how you can use it for sitefactory:

```
style="z-var: sf-background-color: blue | sf-color: red | sf-font-family: 'Impact', Arial;"
```

## Build Commands
`build` tasks is intended for the initial FE development stage:
* run `npm run build` to kick off the build process;
* run `npm run build-watch` and new browser tab will open with a server pointing to your project files;

`inline` tasks doing the same as `build`, but also they are inlining your css into rendered html and make possible test emails in Litmus.
* run `npm run inline` to inline your CSS into your HTML along with the rest of the build process;
* run `npm run inline-watch` the same as `inline` + watching and browsersync tasks;

`export` tasks is for BE developers, this tasks are doing the following:
* put rendered and inlined files in a special folder in a www repo;
* creates a separated file with minified CSS code which contains only media queries;
* put images in default stratis_site images folder;

- run `npm run export` to make magic and put your files in www repo.
- run `npm run export -- --typo3=9` to export in typo3 folder, version 9.
- run `npm run export-watch` includes a watch task (no browsersync, only watch task). It fires every time you save something in `src` folder.

`other tasks`:
* run `npm run double-watch` to open split view, for `build` and `inline` modes, its very useful
for developing

## Helpfull information
[Foundation for Emails DOCs](https://foundation.zurb.com/emails/docs/)

[Litmus: The Ultimate Guide to Web Fonts](https://litmus.com/blog/the-ultimate-guide-to-web-fonts)
#####Full list of email-safe webfonts. Use them as fallback for your custom fonts.
```
- Arial, Helvetica, sans-serif
- 'Arial Black', Gadget, sans-serif
- 'Bookman Old Style', serif
- 'Comic Sans MS', cursive
- Courier, monospace
- 'Courier New', Courier, monospace
- Garamond, serif
- Georgia, serif
- Impact, Charcoal, sans-serif
- 'Lucida Console', Monaco, monospace
- 'Lucida Sans Unicode', 'Lucida Grande', sans-serif
- 'MS Sans Serif', Geneva, sans-serif;
- font-family: 'MS Serif', 'New York', sans-serif;
- 'Palatino Linotype', 'Book Antiqua', Palatino, serif;
- Symbol, sans-serif
- Tahoma, Geneva, sans-serif
- 'Times New Roman', Times, serif
- 'Trebuchet MS', Helvetica, sans-serif
- Verdana, Geneva, sans-serif
- Webdings, sans-serif
- Wingdings, 'Zapf Dingbats', sans-serif
```

