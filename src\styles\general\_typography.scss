// Buttons
.columns table.button {
    table td {
        a {
            text-transform: uppercase;
            white-space: pre;
        }

        img {
            display: inline;
            vertical-align: sub;
        }
    }
}

table.hr {
    max-width: $hr-width;
}

// Horizontal rule
table.h-line {
    margin: $hr-margin;
    Margin: $hr-margin;
    clear: both;
    max-width: $hr-width;

    th {
        border-bottom: $hr-border;
        border-left: 0;
        border-right: 0;
        border-top: 0;
        height: 0;
        line-height: 0;
    }
}

// https://litmus.com/blog/windows-10-mail-email-rendering-support
a {
    text-decoration: none;
}

// https://litmus.com/blog/how-to-fix-blue-links-in-gmail
u + #body a {
    color: inherit;
    text-decoration: none;
    font-size: inherit;
    font-family: inherit;
    font-weight: inherit;
    line-height: inherit;
}
