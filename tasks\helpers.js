const plugins = require('gulp-load-plugins');
const rimraf = require('rimraf');
const browser = require('browser-sync');
const { argv } = require('yargs');
const config = require('../config');

const flags = {
    port: Number(argv.port || argv.p) || 3002,
    typo3v: argv.typo3v === 8 ? 8 : 9,

    env: typeof argv.env === 'string' ? argv.env : 'fe',

    get IS_BE() {
        return this.env === 'typo3' || this.env === 'drupal' || this.env === 'wordpress';
    },
    get IS_TYPO3() {
        return this.env === 'typo3';
    },
    get IS_DRUPAL() {
        return this.env === 'drupal';
    },
    get IS_WORDPRESS() {
        return this.env === 'wordpress';
    },
    get templateEnv() {
        if (this.IS_TYPO3) {
            return 'Typo3';
        }

        if (this.IS_DRUPAL) {
            return 'Drupal';
        }

        if (this.IS_WORDPRESS) {
            return 'Wordpress';
        }

        return 'Fe';
    },
    IS_DEV: !!(argv.dev),
    IS_INLINE: !!(argv.inline),
    IS_DOUBLE: !!(argv.double),
    IS_SILENT: !!(argv.silent),
    IS_NO_INKY: !!(argv.noinky),
};

const $ = {
    plugin: plugins(),

    errorHandler(...args) {
        const arg = Array.prototype.slice.call(args);
        $.plugin.notify.onError({
            title: '<%= error.plugin %>',
            message: '\nmessage:<%= error.message %>\nfileName:<%= error.fileName %>',
            // 'error' object contains:
            // name, message, stack ,fileName, plugin, showProperties, showStack properties
            sound: 'Submarine',
        }).apply(this, arg);
        this.emit('end');
    },

    www: (() => {
        if (flags.env === 'typo3' && !flags.IS_DEV) {
            return config.www[`typo3v${flags.typo3v}`];
        }
        if (flags.env === 'drupal' && !flags.IS_DEV) {
            return config.www.drupal;
        }

        if (flags.env === 'wordpress' && !flags.IS_DEV) {
            return config.www.wordpress;
        }

        return config.www.dev;
    })(),

    imgPath: (() => {
        if (flags.IS_INLINE && !flags.IS_DOUBLE) {
            if (flags.IS_BE) {
                return config.images.inline[flags.env];
            }

            return config.images.inline.jenkins;
        }

        return config.images.inline.fe;
    })(),

    htmlExt: (() => {
        if (flags.env === 'drupal') {
            return '.html.twig';
        }

        if (flags.env === 'wordpress') {
            return '.php';
        }

        return '.html';
    })(),

    parse(str) {
        return !str ? false : str
            .replace('[www]', this.www)
            .replace('[domain]', argv.domain ? argv.domain : config.domain);
    },

    dest(obj) {
        return this.parse(obj[flags.env]);
    },

    reload(done) {
        browser.reload();
        done();
    },

    config,
    ...flags,
};

$.destHTML = $.dest(config.templates.dest);


// Delete the "build" folder contents
$.clean = done => rimraf(`${$.destHTML}**/*`, done);

$.cleanNewsletterCSS = done => rimraf(`${$.destHTML}newsletter-css.php`, done);

// Delete temp files in export folder
$.cleanExport = (done) => {
    if (!flags.IS_NO_INKY) {
        rimraf(`${$.destHTML}app.css`, done);
    }

    return done();
};

module.exports = $;
