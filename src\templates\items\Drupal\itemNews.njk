<container>
    {% raw %}
        {% set item = drupal_entity('node', array.0.target_id, 'newsletter_focus')['#node'] %}
        {% if item %}
            {% set link = item.id() ? path('entity.node.canonical', {'node': item.id()}) : NULL %}
            {% set link = link|path_to_absolute %}
            {% set image = drupal_field('field_media_image', 'node', item.id(), 'newsletter_focus').0['#image_url'] %}
            {% set image_alt = drupal_field('field_media_image', 'node', item.id(), 'newsletter_focus').0['#alt'] %}
            {% set theme = drupal_field('field_categories', 'node', item.id(), 'newsletter_focus') %}
            {% set teaser = drupal_field('field_description', 'node', item.id(), 'newsletter_focus').0['#text']|replace({'<p>':'', '</p>':'' }) %}
            <row>
                <columns class="news-focus first last">
                    {% if image %}
                    <a href="{{ link }}" aria-hidden="true" tabindex="-1">
                        <center>
                            <img src="{{ image }}" alt="{{ image_alt }}">
                        </center>
                    </a>
                    {% endif %}
                    <spacer size="20"></spacer>
                    {% if theme.0 %}
                        <table role="presentation" class="category"><tr><td>{{ theme.0 }}</td></tr></table>
                        <spacer size="8"></spacer>
                    {% endif %}
                    <h3 class="section__title"><a href="{{ link }}">{{ item.title.value }}</a></h3>
                    {% if teaser %}
                        <spacer size="15"></spacer>
                        <p class="section__teaser"><a href="{{ link }}">{{ teaser|truncate(150, true, true) }}</a></p>
                    {% endif %}
                </columns>
            </row>
        {% endif %}
    {% endraw %}
    <spacer size="10"></spacer>
    {% raw %}
    {% for item in array[1:] %}
    <row>
        {% set item = drupal_entity('node', item.target_id, 'newsletter')['#node'] %}

        {% if item %}

        {% set link = item.id() ? path('entity.node.canonical', {'node': item.id()}) : NULL %}
        {% set link = link|path_to_absolute %}
        {% set image = drupal_field('field_media_image', 'node', item.id(), 'newsletter').0['#image_url'] %}
        {% set image_alt = drupal_field('field_media_image', 'node', item.id(), 'newsletter').0['#alt'] %}
        {% set theme = drupal_field('field_categories', 'node', item.id(), 'newsletter') %}
        {% set teaser = drupal_field('field_description', 'node', item.id(), 'newsletter_focus').0['#text']|replace({'<p>':'', '</p>':'' }) %}
            {% if image %}
                <columns large="6" class="first">
                    <a href="{{ link }}" aria-hidden="true" tabindex="-1">
                        <center>
                            <img src="{{ image }}" alt="{{ image_alt }}">
                        </center>
                    </a>
                </columns>
                <columns large="6" class="last">
                    {% if theme.0 %}
                        <table role="presentation" class="category"><tr><td>{{ theme.0 }}</td></tr></table>
                        <spacer size="10"></spacer>
                    {% endif %}
                    <h3 class="section__title"><a href="{{ link }}">{{ item.title.value }}</a></h3>
                    {% if teaser %}
                        <spacer size="15"></spacer>
                        <p class="section__teaser"><a href="{{ link }}">{{ teaser|truncate(150, true, true) }}</a></p>
                    {% endif %}
                </columns>
            {% else %}
                <columns large="12" class="first last">
                    {% if theme.0 %}
                    <table role="presentation" class="category"><tr><td>{{ theme.0 }}</td></tr></table>
                    <spacer size="10"></spacer>
                    {% endif %}
                    <h3 class="section__title"><a href="{{ link }}">{{ item.title.value }}</a></h3>
                    {% if teaser %}
                    <spacer size="15"></spacer>
                    <p class="section__teaser"><a href="{{ link }}">{{ teaser|truncate(150, true, true) }}</a></p>
                    {% endif %}
                </columns>
            {% endif %}
        </row>
        <spacer size="40"></spacer>
        {% endif %}
    {% endfor %}
    {% endraw %}
</container>
