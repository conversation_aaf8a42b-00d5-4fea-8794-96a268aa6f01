{% raw %}
<?php if ( ! empty( $args['events'] ) || ! empty ( $args['focus_posts'] ) ): ?>
    <container>
        <?php if( ! empty ( $args['focus_posts'] ) ): ?>
            <?php foreach (  $args['focus_posts'] as $event ): ?>
                <row>
                    <columns class="first last events-focus">
                        <?php if( ! empty( $event['event_permalink'] ) ): ?>
                        <a href="<?php echo esc_url( $event['event_permalink'] ); ?>" class="events-image" aria-hidden="true"
                           tabindex="-1">
                            <img src="<?php echo esc_url( $event['event_focus_image'] ); ?>" alt=""/>
                        </a>
                        <?php else: ?>
                            <img src="<?php echo esc_url( $event['event_focus_image'] ); ?>" alt=""/>
                        <?php endif; ?>
                        <?php if (  $event['event_has_start_date'] ): ?>
                        <table role="presentation" class="events-date">
                            <tr>
                                <td colspan="12">
                                    <spacer size="15"></spacer>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    <table>
                                        <tr>
                                            <td>
                                                <p class="date date--first">
                                                    <span><?php echo esc_html( $event['event_start_date']['d'] ); ?></span></p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <p class="date date--first"><?php echo esc_html( $event['event_start_date']['M'] ); ?></p>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                                <?php if (  $event['event_has_end_date'] ): ?>
                                <td style="width: 18px; vertical-align: middle; min-width: 18px;" class="divider">
                                    <img src="<?php echo esc_url( $args['focus_separator_icon_url'] ); ?>" alt="">
                                </td>
                                <td>
                                    <table>
                                        <tr>
                                            <td>
                                                <p class="date date--first">
                                                    <span><?php echo esc_html( $event['event_end_date']['d'] ); ?></span></p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <p class="date date--first"><?php echo esc_html( $event['event_end_date']['M'] ); ?></p>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                                <?php endif; ?>
                                <td style="width: 80%;">&nbsp;</td>
                            </tr>
                            <tr>
                                <td colspan="12">
                                    <spacer size="15"></spacer>
                                </td>
                            </tr>
                            <?php endif; ?>
                        </table>
                        <spacer size="20"></spacer>
                        <?php if ( ! empty( $event['event_terms_blockpost'] ) ): ?>
                        <table role="presentation" class="category">
                            <tr>
                                <td class="category"><?php echo $event['event_terms_blockpost']; ?></td>
                            </tr>
                        </table>
                        <?php endif; ?>
                        <spacer size="10"></spacer>
                        <h3 class="section__title">
                            <?php if ( ! empty( $event['event_permalink'] ) ): ?>
                            <a href="<?php echo esc_url( $event['event_permalink'] ); ?>"><?php echo esc_html( $event['event_title'] ); ?></a>
                            <?php else: ?>
                            <?php echo esc_html( $event['event_title'] ); ?>
                            <?php endif; ?>
                        </h3>
                        <spacer size="15"></spacer>
                        <table role="presentation" class="events__timeplace">
                            <?php if ( '00h00' !== $event['event_start_date']['H\hi'] ): ?>
                            <tr>
                                <td class="events__icon"><img src="<?php echo esc_url( $args['clock_icon_url'] ); ?>" alt="">
                                </td>
                                <td class="events__time">
                                    <?php
                                                            echo esc_html( $event['event_start_date']['H\hi'] );
                                                            if ( $event['start_time_not_equal_end_time'] ) {
                                                                echo esc_html( '&nbsp;' ) . esc_html( $args['to_text'] ) . esc_html( '&nbsp;' );
                                                                echo esc_html( $event['event_end_date']['H\hi'] );
                                                            }
                                                        ?>
                                </td>
                            </tr>
                            <?php endif; ?>
                            <?php if ( ! empty( $event['event_locations_title'] ) ): ?>
                            <tr>
                                <td class="events__icon"><img src="<?php echo esc_url( $args['map_marker_icon_url'] ); ?>"
                                                              alt=""></td>
                                <td class="events__place">
                                    <?php echo esc_html( $event['event_locations_title'] ); ?>
                                </td>
                            </tr>
                            <?php endif; ?>
                        </table>
                    </columns>
                </row>
            <?php endforeach; ?>
        <?php endif; ?>
        <?php if( ! empty ( $args['events'] ) ): ?>
            <?php foreach (  $args['events'] as $event ): ?>
                <row class="events-item">
                    <columns large="6" class="first">
                        <table role="presentation">
                            <tr>
                                <?php if ( !empty ( $event['event_image'] ) ): ?>
                                <td class="events-image">
                                    <?php if ( ! empty( $event['event_permalink'] ) ): ?>
                                    <a href="<?php echo esc_url( $event['event_permalink'] ); ?>" aria-hidden="true" tabindex="-1">
                                        <img src="<?php echo esc_url( $event['event_image'] ); ?>" alt=""/>
                                    </a>
                                    <?php else: ?>
                                        <img src="<?php echo esc_url( $event['event_image'] ); ?>" alt=""/>
                                    <?php endif; ?>
                                </td>
                                <?php endif; ?>
                                <?php if (  $event['event_has_start_date'] ): ?>
                                <td style="width: 100px;" class="events-date">
                                    <a href="<?php echo esc_url( $event['event_permalink'] ); ?>">
                                        <table>
                                            <tr>
                                                <td>
                                                    <spacer size="30"></spacer>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <p class="date date--first">
                                                        <span><?php echo esc_html( $event['event_start_date']['d'] ); ?></span>
                                                    </p>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <p class="date date--first"><?php echo esc_html( $event['event_start_date']['M'] ); ?></p>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <spacer size="7"></spacer>
                                                </td>
                                            </tr>
                                            <?php if (  $event['event_has_end_date'] ): ?>
                                            <tr>
                                                <td>
                                                    <center>
                                                        <img src="<?php echo esc_url( $args['separator_icon_url'] ); ?>"
                                                             class="float-center" align="center" alt="">
                                                    </center>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <spacer size="7"></spacer>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <p class="date">
                                                        <span><?php echo esc_html( $event['event_end_date']['d'] ); ?></span>
                                                    </p>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>
                                                    <p class="date"><?php echo esc_html( $event['event_end_date']['M'] ); ?></p>
                                                </td>
                                            </tr>
                                            <?php endif; ?>
                                            <tr>
                                                <td>
                                                    <spacer size="10"></spacer>
                                                </td>
                                            </tr>
                                        </table>
                                    </a>
                                </td>
                                <?php endif; ?>
                            </tr>
                        </table>
                    </columns>
                    <columns large="6" class="last">
                        <?php if ( ! empty( $event['event_terms_blockpost'] ) ): ?>
                        <table role="presentation" class="category">
                            <tr>
                                <td>
                                    <?php echo $event['event_terms_blockpost']; ?>
                                </td>
                            </tr>
                        </table>
                        <?php endif; ?>
                        <spacer size="10"></spacer>
                        <h3 class="section__title">
                            <?php if ( !empty( $event['event_permalink'] ) ): ?>
                            <a href="<?php echo esc_url( $event['event_permalink'] ); ?>"><?php echo esc_html( $event['event_title'] ); ?></a>
                            <?php else: ?>
                            <?php echo esc_html( $event['event_title'] ); ?>
                            <?php endif; ?>
                        </h3>
                        <spacer size="15"></spacer>
                        <?php if ( $event['event_start_date']['H\hi'] || $event['event_has_locations'] ): ?>
                        <table role="presentation" class="events__timeplace">
                            <?php if ( '00h00' !== $event['event_start_date']['H\hi'] ): ?>
                            <tr>
                                <td class="events__icon"><img src="<?php echo esc_url( $args['clock_icon_url'] ); ?>" alt="">
                                </td>
                                <td class="events__time">
                                    <?php
                                                        echo esc_html( $event['event_start_date']['H\hi'] );
                                                        if ( $event['start_time_not_equal_end_time'] ) {
                                                            echo esc_html( '&nbsp;' ) . esc_html( $args['to_text'] ) . esc_html( '&nbsp;' );
                                                            echo esc_html( $event['event_end_date']['H\hi'] );
                                                        }
                                                    ?>
                                </td>
                            </tr>
                            <?php endif; ?>
                            <?php if ( ! empty( $event['event_locations_title'] ) ): ?>
                            <tr>
                                <td class="events__icon"><img src="<?php echo esc_url( $args['map_marker_icon_url'] ); ?>"
                                                              alt=""></td>
                                <td class="events__place">
                                    <?php echo esc_html( $event['event_locations_title'] ); ?>
                                </td>
                            </tr>
                            <?php endif; ?>
                        </table>
                        <?php endif; ?>
                    </columns>
                </row>
            <?php endforeach; ?>
        <?php endif; ?>
    </container>
<?php endif; ?>
{% endraw %}
