<container>
    {% raw %}
    {% for item in array %}
        {% set item = drupal_entity('node', item.target_id, 'newsletter')['#node'] %}
        {% if item %}
            {% set link = item.id() ? path('entity.node.canonical', {'node': item.id()}) : NULL %}
            {% set link = link|path_to_absolute %}
            {% set image = drupal_field('field_medias', 'node', item.id(), 'newsletter').0['#image_url'] %}
            {% set image_alt = drupal_field('field_media_image', 'node', item.id(), 'newsletter').0['#alt'] %}
            {% set theme = drupal_field('field_categories', 'node', item.id(), 'newsletter') %}
            {% set teaser = drupal_field('field_description', 'node', item.id(), 'newsletter_focus').0['#text']|replace({'<p>':'', '</p>':'' }) %}
            {% set count = stratis_gallery_get_count(item) %}
            <row>
                {% if image %}
                    <columns large="8" class="first">
                        <a href="{{ link }}" aria-hidden="true" tabindex="-1">
                            <img src="{{ image }}" width="410" height="270" {% if image_alt %} alt="{{ image_alt }}" {%else%}alt{%endif%}>
                        </a>
                    </columns>
                    <columns large="4" class="last">
                        {% if theme.0 %}
                            <table role="presentation" class="category"><tr><td>{{ theme.0 }}</td></tr></table>
                            <spacer size="5"></spacer>
                        {% endif %}
                        <h3 class="section__title"><a href="{{ link }}">{{ item.title.value }}</a></h3>
                        <spacer size="10"></spacer>
                        {% if teaser %}
                            <p class="section__teaser"><a href="{{ link }}">{{ teaser|truncate(150, true, true) }}</a></p>
                            <spacer size="10"></spacer>
                        {% endif %}
                        {% if count %}
                            <table role="presentation">
                                <tr>
                                    <td style="width: 26px;"><img src="{{ site_url }}/images/icons/camera.png" alt></td>
                                    <td class="albums__counter">{{ count }}</td>
                                </tr>
                            </table>
                        {% endif %}
                    </columns>
                {% else %}
                    <columns large="4" class="last">
                        {% if theme.0 %}
                            <table role="presentation" class="category"><tr><td>{{ theme.0 }}</td></tr></table>
                            <spacer size="5"></spacer>
                        {% endif %}
                        <h3 class="section__title"><a href="{{ link }}">{{ item.title.value }}</a></h3>
                        <spacer size="10"></spacer>
                        {% if teaser %}
                            <p class="section__teaser"><a href="{{ link }}">{{ teaser|truncate(150, true, true) }}</a></p>
                            <spacer size="10"></spacer>
                        {% endif %}
                        {% if count %}
                            <table role="presentation">
                                <tr>
                                    <td style="width: 26px;"><img src="{{ site_url }}/images/icons/camera.png" alt></td>
                                    <td class="albums__counter">{{ count }}</td>
                                </tr>
                            </table>
                        {% endif %}
                    </columns>
                {% endif %}
            </row>
            <spacer size="34"></spacer>
        {% endif %}
    {% endfor %}
    {% endraw %}
</container>
