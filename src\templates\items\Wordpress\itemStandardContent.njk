{% raw %}
    <?php if ( ! empty( $args['items'] ) ): ?>
        <container class="section-item">
            <?php foreach( $args['items'] as $item ): ?>
                <row>
                    <?php if ( ! empty( $item['item_image'] ) ): ?>
                        <columns large="6" class="first section-image">
                            <?php if ( ! empty( $item['item_link'] ) ): ?>
                                <a href="<?php echo esc_url( $item['item_link'] ); ?>">
                                    <img src="<?php echo esc_url( $item['item_image'] ); ?>" alt="">
                                </a>
                            <?php else: ?>
                                <img src="<?php echo esc_url( $item['item_image'] ); ?>" alt="">
                            <?php endif; ?>
                        </columns>
                        <columns large="6" class="last section-content">
                            <h3 class="section__title">
                                <?php if ( ! empty( $item['item_link'] ) ): ?>
                                    <a href="<?php echo esc_url( $item['item_link'] ); ?>"><?php echo esc_html( $item['item_title'] ); ?></a>
                                <?php else: ?>
                                    <?php echo esc_html( $item['item_title'] ); ?>
                                <?php endif; ?>
                            </h3>
                            <spacer size="15"></spacer>
                            <p class="section__teaser">
                                <?php if ( ! empty( $item['item_link'] ) ): ?>
                                    <a href="<?php echo esc_url( $item['item_link'] ); ?>"><?php echo esc_html( $item['item_text'] ); ?></a>
                                <?php else: ?>
                                    <?php echo esc_html( $item['item_text'] ); ?>
                                <?php endif; ?>
                            </p>
                        </columns>
                    <?php else: ?>
                        <columns large="12" class="first last section-content">
                            <h3 class="section__title">
                                <?php if ( ! empty( $item['item_link'] ) ): ?>
                                    <a href="<?php echo esc_url( $item['item_link'] ); ?>"><?php echo esc_html( $item['item_title'] ); ?></a>
                                <?php else: ?>
                                    <?php echo esc_html( $item['item_title'] ); ?>
                                <?php endif; ?>
                            </h3>
                            <spacer size="15"></spacer>
                            <p class="section__teaser">
                                <?php if ( ! empty( $item['item_link'] ) ): ?>
                                    <a href="<?php echo esc_url( $item['item_link'] ); ?>"><?php echo esc_html( $item['item_text'] ); ?></a>
                                <?php else: ?>
                                <?php echo esc_html( $item['item_text'] ); ?>
                                <?php endif; ?>
                            </p>
                        </columns>
                    <?php endif; ?>
                </row>
                <spacer size="20"></spacer>
            <?php endforeach; ?>
        </container>
    <?php endif; ?>
{% endraw %}
