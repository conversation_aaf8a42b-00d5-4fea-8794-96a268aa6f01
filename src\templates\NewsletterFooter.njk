{% import "helpers/mixins.njk" as mixins with context -%}
{{- mixins.includeCss(isTemplateIncluded) -}}
{% if isTYPO3 %}{% raw %}{namespace v=FluidTYPO3\Vhs\ViewHelpers}{% endraw %}{% endif -%}
{% set footer = {
    archiveLink: "{archiveLink}||{{ link_list }}||<?php echo esc_url( $args['archive_link'] ); ?>||#",
    archiveLinkTitle: "{archiveLinkTitle}||{{ 'Archives'|t }}||<?php echo esc_html( $args['archive_link_title'] ); ?>||Archives",
    unsubscribeLink: "{unsubscribeLink}||{{ link_unsubscribe }}||<?php echo esc_url( $args['unsubscribe_link'] ); ?>||#",
    unsubscribeLinkTitle: "{unsubscribeLinkTitle}||{{ 'Unsubscribe'|t }}||<?php echo esc_html( $args['unsubscribe_link_title'] ); ?>||Désinscription",
    text: "{name}||{{ title }}||<?php echo esc_html( $args['name'] ); ?>||Nom du client lorem ipsum dolor sit amet",
    address: "{address -> f:format.nl2br() -> f:format.raw()}||{{ address }}||<?php echo esc_html( $args['address'] ); ?>||00 Rue dolor sit amet,<br>123456 Villeloremipsum",
    address_code: "||{{ address_code }}||<?php echo esc_html( $args['zip'] ); ?>||",
    address_city: "||{{ address_city }}||<?php echo esc_html( $args['city'] ); ?>||",
    picture: "{logo}||{{ logo }}||<?php echo esc_url( $args['logo_url'] ); ?>||{{ imgPath }}logo-footer.png",
    pictureAlt: "{logoAlt}||{{ logoAlt }}||<?php echo esc_attr( $args['logo_alt'] ); ?>||[CLIENT'S NAME] (retour à l'accueil)",
    pictoLink: "{logoLink}||{{ home_link }}||<?php echo esc_url( $args['home_link'] ); ?>||#",
    textBig: '{text -> f:format.nl2br() -> f:format.raw()}||{{ body|raw }}||<?php echo $args[\'text\']; ?>||Conformément au Règlement européen sur la protection des données personnelles et à la Loi Informatique et Libertés, vous pouvez à tout moment consulter notre politique en la matière et exercer vos droits en prenant contact avec nos équipes. <br>Pour toute demande : <span style="color: #ffffff"><EMAIL></span> <br><a href="#">Consulter notre politique de protection des données personnelles. </a>',
    infos1: {
        class: 'footer-email',
        link: '{email}||{{ site }}||<?php echo esc_url( $args[\'contact_page_link\'] ); ?>||mailto:<EMAIL>',
        icon: '{emailIcon}||{{ site_url }}/images/icons/envelope.png||<?php echo esc_url( $args[\'contact_page_icon\'] ); ?>||{{ imgPath }}icons/envelope.png',
        text: '{emailTitle}||{{- site_text -}}||<?php echo esc_html( $args[\'contact_page_link_title\'] ); ?>||Nous contacter'
    }
} | parseBE %}

<footer role="contentinfo">
<center>
    <wrapper class="footer">
        <container>
            <spacer size="70"></spacer>
            <row>
                <columns large="4" class="first footer-aside" valign="top">
                    {% if isTYPO3 %}{% raw %}<f:if condition="{logo}">{% endraw %}{% endif -%}
                    {% if isWORDPRESS %}{% raw %}<?php if( $args['logo_url'] ): ?>{% endraw %}{% endif -%}
                        <h2 class="sr-only">Information du site</h2><a href="{{ footer.pictoLink }}" class="footer-aside__logo"><img src="{{ footer.picture }}" width="246" height="92" alt="{{ footer.pictureAlt }}" class="small-float-center"></a>
                    {% if isTYPO3 %}{% raw %}</f:if>{% endraw %}{% endif -%}
                    {% if isWORDPRESS %}{% raw %}<?php endif; ?>{% endraw %}{% endif -%}
                    {% if isTYPO3 %}{% raw %}<f:if condition="{name}">{% endraw %}{% endif -%}
                    {% if isDRUPAL %}{% raw %}{% if title %}{% endraw %}{% endif -%}
                    {% if isWORDPRESS %}{% raw %}<?php if( $args['name'] ): ?>{% endraw %}{% endif -%}
                        <p class="footer-title small-text-center"><strong>{{ footer.text }}</strong></p>
                    {% if isTYPO3 %}{% raw %}</f:if>{% endraw %}{% endif -%}
                    {% if isDRUPAL %}{% raw %}{% endif %}{% endraw %}{% endif -%}
                    {% if isWORDPRESS %}{% raw %}<?php endif; ?>{% endraw %}{% endif -%}
                    {% if isDRUPAL %}
                        {% raw %}{% if address %}{% endraw %}
                            <p class="footer-address small-text-center">{{ footer.address }}<br>{{ footer.address_code }} {{ footer.address_city }}</p>
                        {% raw %}{% endif %}{% endraw %}
                    {% elif isWORDPRESS %}
                        {% if isWORDPRESS %}{% raw %}<?php if( $args['address'] ): ?>{% endraw %}{% endif -%}
                            <p class="footer-address small-text-center">{{ footer.address }}<br>{{ footer.address_code }} {{ footer.address_city }}</p>
                        {% if isWORDPRESS %}{% raw %}<?php endif; ?>{% endraw %}{% endif -%}
                    {% else %}
                        {% if isTYPO3 %}{% raw %}<f:if condition="{address}">{% endraw %}{% endif -%}
                            <p class="footer-address small-text-center">{{ footer.address }}</p>
                        {% if isTYPO3 %}{% raw %}</f:if>{% endraw %}{% endif -%}
                    {% endif -%}
                    {% if isDRUPAL %}{% raw %}{% if site %}{% endraw %}{% endif -%}
                    {% if isTYPO3 %}{% raw %}<f:if condition="{email}">{% endraw %}{% endif -%}
                    {% if isWORDPRESS %}{% raw %}<?php if( $args['contact_page_link'] ): ?>{% endraw %}{% endif -%}
                    <spacer size="22"></spacer>
                    <container class="section-button">
                        <row>
                            <columns class="first last section-button-column set-right-justified">
                                <button href="{{ footer.infos1.link }}" class="large {{ footer.infos1.class }}"><img src="{{ footer.infos1.icon }}" alt="">  {{ footer.infos1.text }}</button>
                            </columns>
                        </row>
                    </container>
                    <spacer size="20"></spacer>
                    {% if isTYPO3 %}{% raw %}</f:if>{% endraw %}{% endif -%}
                    {% if isDRUPAL %}{% raw %}{% endif %}{% endraw %}{% endif -%}
                    {% if isWORDPRESS %}{% raw %}<?php endif; ?>{% endraw %}{% endif -%}
                </columns>
                <columns large="8" class="last text-big" valign="top">
                    <spacer size="12"></spacer>
                    {% include "items/{{ env }}/itemFooterSocial.njk" | interpolate -%}
                    <spacer size="40"></spacer>
                    {% if isDRUPAL %}{% raw %}{% if body %}{% endraw %}{% endif -%}
                    {% if isTYPO3 %}{% raw %}<f:if condition="{text}">{% endraw %}{% endif -%}
                    {% if isWORDPRESS %}{% raw %}<?php if( $args['text'] ): ?>{% endraw %}{% endif -%}
                        <p class="small-text-center">{{ footer.textBig }}</p>
                        <spacer size="15"></spacer>
                    {% if isTYPO3 %}{% raw %}</f:if>{% endraw %}{% endif -%}
                    {% if isDRUPAL %}{% raw %}{% endif %}{% endraw %}{% endif -%}
                    {% if isWORDPRESS %}{% raw %}<?php endif; ?>{% endraw %}{% endif -%}
                    <p class="small-text-center"><a href="{{ footer.archiveLink }}" class="archi">{{ footer.archiveLinkTitle }}</a> | <a href="{{ footer.unsubscribeLink }}" class="désins">{{ footer.unsubscribeLinkTitle }}</a></p>
                </columns>
            </row>
            <spacer size="70"></spacer>
        </container>
    </wrapper>
</center>
</footer>
