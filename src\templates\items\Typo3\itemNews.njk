{% raw %}
    <f:if condition="{news}">
        <container>
            <f:for each="{news}" as="newsItem">
                <row>
                    {n:link(newsItem:newsItem,settings:settings,uriOnly:'1') -> v:variable.set(name:'relativeLinkToSingle')}
                    {f:uri.typolink(parameter:relativeLinkToSingle,absolute:1) -> v:variable.set(name:'linkToSingle')}
                    <columns large="6" class="first">
                        <f:if condition="{newsItem.mainMedia} || {newsItem.falMedia}">
                            <a href="{linkToSingle}" aria-hidden="true" tabindex="-1">
                                <center>
                                    <img src="{f:render(partial:'Common/ListImageNewsletter', section:'MainOrFalImage', arguments:'{newsItem:newsItem,width:\'320c\',height:\'212c\'}') -> v:format.trim()}" alt=""/>
                                </center>
                            </a>
                        </f:if>
                    </columns>
                    <columns large="6" class="last">
                        <f:if condition="{newsItem.categories}">
                            <table role="presentation" class="category">
                                <tr>
                                    <td>{v:render.template(file:'EXT:stratis_site/Resources/Private/Partials/Common/InlineList.html',variables:'{records:newsItem.categories, glue:\', \'}')}</td>
                                </tr>
                            </table>
                            <spacer size="10"></spacer>
                        </f:if>
                        <h3 class="section__title">
                            <a href="{linkToSingle}" title="{newsItem.title}">{newsItem.title}</a>
                        </h3>
                        <spacer size="15"></spacer>
                        <f:if condition="{newsItem.teaser} || {newsItem.bodytext}">
                            <p class="section__teaser">{f:render(partial:'Extensions/news/List/Common/DisplayTeaser', section:'DisplayTeaser', arguments:'{newsItem:newsItem, settings:settings}')}</p>
                        </f:if>
                    </columns>
                </row>
                <spacer size="40"></spacer>
            </f:for>
        </container>
    </f:if>
{% endraw %}
