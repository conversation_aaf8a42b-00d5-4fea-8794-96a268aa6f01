const gulp = require('gulp');
const templates = require('./tasks/templates');
const styles = require('./tasks/styles');
const inline = require('./tasks/inline');
const images = require('./tasks/images');
const server = require('./tasks/server');
const open = require('./tasks/open');
const $ = require('./tasks/helpers');

// Watch for file changes
const watch = () => {
    gulp.watch($.config.images.listen, gulp.series(images, $.reload));
    gulp.watch($.config.templates.listen, gulp.series(templates, inline, $.reload));
    if ($.IS_INLINE) {
        gulp.watch($.config.styles.listen, gulp.series(templates, styles, inline, $.reload));
    } else {
        gulp.watch($.config.styles.listen, gulp.series(styles));
    }
};

const watchExport = () => {
    gulp.watch('src/**/*', gulp.series('export'));
};

// Build the "build" folder by running all of the above tasks
gulp.task('build',
    gulp.parallel(gulp.series(templates, styles, inline), images));

gulp.task('default',
    gulp.series('build', server, open, watch));

gulp.task('export',
    gulp.series($.cleanNewsletterCSS, 'build', $.cleanExport));

gulp.task('export-watch',
    gulp.series('export', watchExport));
