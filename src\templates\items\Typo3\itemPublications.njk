<container class="section-item">
    {% raw %}
        <f:for each="{news}" as="newsItem">
            {v:variable.unset(name:'image')}
            {n:link(newsItem:newsItem,settings:settings,uriOnly:'1')-> v:variable.set(name:'relativeLinkToSingle')}
            {f:uri.typolink(parameter:relativeLinkToSingle,absolute:1) -> v:variable.set(name:'linkToSingle')}
            <f:if condition="{newsItem.falRelatedFiles}">
                {newsItem.falRelatedFiles -> v:iterator.first() -> v:variable.set(name:'fileElement')}
                {f:uri.typolink(parameter:fileElement.originalResource.publicUrl,absolute:1) -> v:variable.set(name:'linkToFile')}
            </f:if>
            <row>
                <f:if condition="{newsItem.mainMedia} || {newsItem.falMedia}">
                    <columns large="4" class="first section-image">
                        <img src="{f:render(partial:'Common/ListImageNewsletter', section:'MainOrFalImage', arguments:'{newsItem:newsItem,width:\'196c\',height:\'280c\'}') -> v:format.trim()}" alt=""/>
                    </columns>
                </f:if>
                <columns large="8" class="last section-content">
                    <f:if condition="{newsItem.categories}">
                        <table role="presentation" class="category">
                            <tr>
                                <td>{v:render.template(file:'EXT:stratis_site/Resources/Private/Partials/Common/InlineList.html',variables:'{records:newsItem.categories, glue:\', \'}')}</td>
                            </tr>
                        </table>
                        <spacer size="5"></spacer>
                    </f:if>
                    <h3 class="section__title">
                        <a href="{linkToSingle}">{newsItem.title}</a>
                    </h3>
                    <spacer size="10"></spacer>
                    <f:if condition="{newsItem.teaser} || {newsItem.bodytext}">
                        <p class="section__teaser">
                            {f:render(partial:'Extensions/news/List/Common/DisplayTeaser', section:'DisplayTeaser', arguments:'{newsItem:newsItem, settings:settings}')}
                        </p>
                    </f:if>
                    <f:if condition="{fileElement}">
                        <f:alias map="{originalFileElement: fileElement.originalResource.originalFile.properties}">
                            <table role="presentation" class="publications__links">
                                <tr>
                                    <td class="publications__icon">
                                        <a href="{linkToFile}"
                                           title="{f:translate(key:'common.download')} :  {fileElement.title -> v:or(alternative:originalFileElement.name)} (.{originalFileElement.extension}, {originalFileElement.size -> f:format.bytes(decimals: 1, decimalSeparator: ',')})"
                                           target="_blank">
                                            <img src="{f:uri.image(absolute:1,src:'{pathToImages}newsletter/icons/read-download.png')}" alt="">
                                        </a>
                                    </td>
                                    <td class="publications__text">
                                        <a href="{linkToFile}"
                                           title="{f:translate(key:'common.download')} :  {fileElement.title -> v:or(alternative:originalFileElement.name)} (.{originalFileElement.extension}, {originalFileElement.size -> f:format.bytes(decimals: 1, decimalSeparator: ',')})"
                                           target="_blank">
                                            {f:translate(key:'common.download')} <span>{originalFileElement.extension -> f:format.case(mode:'upper')} - {originalFileElement.size -> f:format.bytes(decimals: 1, decimalSeparator: ',')}</span>
                                        </a>
                                    </td>
                                </tr>
                                <f:if condition="{fileElement.description}">
                                    <tr>
                                        <td class="publications__icon">
                                            <a href="{f:uri.page(pageUid:fileElement.description)}" target="_blank">
                                                <img src="{f:uri.image(absolute:1,src:'{pathToImages}newsletter/icons/read-view.png')}" alt="">
                                            </a>
                                        </td>
                                        <td class="publications__text">
                                            <a href="{f:uri.page(pageUid:fileElement.description)}" target="_blank">
                                                {f:translate(key:'publications.read')}
                                            </a>
                                        </td>
                                    </tr>
                                </f:if>
                            </table>
                        </f:alias>
                    </f:if>
                </columns>
            </row>
            <spacer size="34"></spacer>
        </f:for>
    {% endraw %}
</container>
