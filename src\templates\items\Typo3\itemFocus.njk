{% raw %}
    <f:if condition="{pages}">
        <f:for each="{pages -> v:iterator.explode(glue:',')}" as="page">
            {v:variable.unset(name:'media')}
            {v:variable.unset(name:'imageOrigin')}
            {v:variable.unset(name:'image')}
            {v:variable.unset(name:'imageAlt')}
            {v:variable.unset(name:'subtitle')}
            {v:variable.unset(name:'pageTitle')}
            {v:variable.unset(name:'description')}
            {v:variable.unset(name:'link')}
            {v:variable.set(name:'media',value:'{v:page.resources.fal(table:\'pages\', field:\'media\', uid:page) -> v:iterator.first()}')}
            {v:resource.file(treatIdAsReference: 1, identifier:media.uid) -> v:variable.set(name: 'imageOrigin')}
            <f:if condition="{imageOrigin}">
                {f:uri.image(absolute:1,src:imageOrigin.url,width:'620c',height:'413c') -> v:variable.set(name: 'image')}
                {media.alternative -> v:or(alternative:imageOrigin.alternative) -> v:variable.set(name:'imageAlt')}
            </f:if>
            {v:page.info(pageUid: page, field: 'title') -> v:variable.set(name: 'pageTitle')}
            {v:page.info(pageUid: page, field: 'subtitle') -> v:variable.set(name: 'subtitle')}
            {v:page.info(pageUid: page, field: 'description') -> v:variable.set(name: 'description')}
            {f:uri.typolink(parameter:page,absolute:1) -> v:variable.set(name:'link')}
            <f:if condition="{image}">
                <row>
                    <columns class="first last section-image">
                        <center>
                            <a href="{link}">
                                <img src="{image}" alt="{imageAlt}">
                            </a>
                        </center>
                    </columns>
                </row>
            </f:if>
            <row>
                <columns class="first last section-content">
                    <f:if condition="{subtitle}">
                        <p class="section__subtitle"><a href="{link}">{subtitle}</a></p>
                        <spacer size="5"></spacer>
                    </f:if>
                    <h3 class="section__title"><a href="{link}">{pageTitle}</a></h3>
                    <f:if condition="{description}">
                        <spacer size="15"></spacer>
                        <p class="section__teaser"><a href="{link}">{description -> f:format.nl2br()}</a></p>
                    </f:if>
                </columns>
            </row>
        </f:for>
    </f:if>
{% endraw %}
