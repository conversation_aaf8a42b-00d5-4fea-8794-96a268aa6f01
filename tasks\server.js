const browser = require('browser-sync');
const $ = require('./helpers');

const server = (done) => {
    browser.init({
        server: $.destHTML,
        port: $.port,
        open: !$.IS_SILENT,
        watchOptions: {
            // This introduces a small delay when watching for file
            // change events to avoid triggering too many reloads
            debounceDelay: 1000,
        },
    });
    done();
};

module.exports = server;
