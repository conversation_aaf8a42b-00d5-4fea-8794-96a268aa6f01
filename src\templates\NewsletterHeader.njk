{% import "helpers/mixins.njk" as mixins with context -%}
{{- mixins.includeCss(isTemplateIncluded) -}}
{% set header = {
    headerText: "{headerText}||Si le message ci-dessous ne s'affiche pas correctement,||<?php echo esc_html( $args['header_text'] ); ?>||Si le message ci-dessous ne s'affiche pas correctement,",
    headerLink: "{headerLink}||{{ path('<current>')|path_to_absolute }}||<?php echo esc_url( $args['header_link'] ); ?>||#",
    headerLinkText: "{headerLinkText}|| consultez la version en ligne.||<?php echo esc_html( $args['header_link_text'] ); ?>||consultez la version en ligne.",
    title: "{title}||{{ title }}||<?php echo esc_html( $args['title'] ); ?>||Lettre d'information",
    number: "{number}||{{ number }}||<?php echo esc_html( $args['title_number'] ); ?>||#105",
    date: "{date}||{{ date }}||<?php echo esc_html( $args['date'] ); ?>||Septembre 2020",
    link: "{link}||{{ home_link }}||<?php echo esc_url( $args['home_link'] ); ?>||#",
    picture: "{picto}||{{ logo }}||<?php echo esc_url( $args['logo_url'] ); ?>||{{ imgPath }}logo.png",
    pictoAlt: "{pictoAlt}||{{ logo_alt }}||<?php echo esc_attr( $args['logo_alt'] ); ?>||[CLIENT'S NAME] (retour à l'accueil)"
} | parseBE -%}

<header role="banner">
<center>
    {#<wrapper class="header-topline" style="z-var: color;">#}
    <wrapper class="header-topline">
        <container>
            <row>
                <columns class="first last">
                    <p class="header-topline__text text-center">{{ header.headerText }} <a href="{{ header.headerLink }}">{{ header.headerLinkText }}</a></p>
                </columns>
            </row>
        </container>
    </wrapper>
    {#
        z-var can get a style sting but each style property should start with 'sf-' prefix
    #}
    <wrapper class="header">
        <container>
            <spacer size="4"></spacer>
            <row>
                {% if isTYPO3 %}{% raw %}<f:if condition="{picto}">{% endraw %}{% endif -%}
                {% if isDRUPAL %}{% raw %}{% if logo %}{% endraw %}{% endif -%}
                {% if isWORDPRESS %}{% raw %}<?php if( $args['logo_url'] ): ?>{% endraw %}{% endif -%}
                    <columns large="7" class="first header-image" valign="middle">
                        <a href="{{ header.link }}" target="_blank">
                            <img src="{{ header.picture }}" width="332" height="125" alt="{{ header.pictoAlt }}">
                        </a>
                    <hr align="center" width="55" size="1" aria-hidden="true">

                    </columns>
                {% if isTYPO3 %}{% raw %}</f:if>{% endraw %}{% endif -%}
                {% if isDRUPAL %}{% raw %}{% endif %}{% endraw %}{% endif -%}
                {% if isWORDPRESS %}{% raw %}<?php endif; ?>{% endraw %}{% endif -%}
                    <columns large="5" class="last header-title" valign="middle">
                        <h1><strong>{{ header.title }}</strong> {{ header.number }}<span>{{ header.date }}</span></h1>
                    </columns>
            </row>
            <spacer size="12"></spacer>
        </container>
    </wrapper>
</center>
</header>
