<container class="section-item">
    {% raw %}
        <?php if ( ! empty( $args['publications'] ) ): ?>
        <?php foreach( $args['publications'] as $publication ): ?>
        <row>
            <?php if ( ! empty( $publication['publication_image'] ) ): ?>
                <columns large="4" class="first section-image">
                    <?php if ( ! empty( $publication['publication_permalink'] ) ) : ?>
                        <a href="<?php echo esc_url( $publication['publication_permalink'] ); ?>">
                            <img src="<?php echo esc_url( $publication['publication_image'] ); ?>" alt=""/>
                        </a>
                    <?php else: ?>
                        <img src="<?php echo esc_url( $publication['publication_image'] ); ?>" alt=""/>
                    <?php endif; ?>
                </columns>
            <?php endif; ?>
            <columns large="8" class="last section-content">
                <?php if ( ! empty( $publication['publication_terms_blockpost'] ) ): ?>
                    <table role="presentation" class="category">
                        <tr>
                            <td>
                                <?php echo $publication['publication_terms_blockpost']; ?>
                            </td>
                        </tr>
                    </table>
                <?php endif; ?>
                <spacer size="5"></spacer>
                <h3 class="section__title">
                    <?php if ( ! empty( $publication['publication_permalink'] ) ) : ?>
                        <a href="<?php echo esc_url( $publication['publication_permalink'] ); ?>">
                            <?php echo esc_html( $publication['publication_title'] ); ?>
                        </a>
                    <?php else: ?>
                        <?php echo esc_html( $publication['publication_title'] ); ?>
                    <?php endif; ?>
                </h3>
                <spacer size="10"></spacer>
                <?php if ( ! empty( $publication['publication_lead_text'] ) ): ?>
                    <p class="section__teaser">
                        <?php echo esc_html( $publication['publication_lead_text'] ); ?>
                    </p>
                <?php endif; ?>
                <?php if ( $publication['publication_count'] > 0 && ! empty( $publication['attachment'] ) ): ?>
                <table role="presentation" class="publications__links">
                    <?php if ( $publication['publication_count'] == 1 ): ?>
                    <?php if ( ! empty( $publication['attachment']['file_url'] ) && ! empty( $publication['attachment']['file_title'] ) ): ?>
                    <tr>
                        <td class="publications__icon">
                            <a href="<?php echo esc_url( $publication['attachment']['file_url'] ); ?>" target="_blank">
                                <img src="<?php echo esc_url( $publication['attachment']['file_icon'] ); ?>" alt="">
                            </a>
                        </td>
                        <td class="publications__text">
                            <a href="<?php echo esc_url( $publication['attachment']['file_url'] ); ?>" target="_blank">
                                <?php echo esc_attr( $publication['attachment']['file_title'] ); ?>
                                <?php if ( ! empty( $publication['attachment']['file_size'] ) ): ?>
                                <span>
                                                            <?php  echo esc_html( $publication['attachment']['file_ext'] );
                                                            echo esc_html( ' - ' . $publication['attachment']['file_size'] );
                                                            ?>
                                                        </span>
                                <?php endif; ?>
                            </a>
                        </td>
                    </tr>
                    <?php endif; ?>
                    <?php if ( ! empty( $publication['attachment']['link_url'] ) && ! empty( $publication['attachment']['link_title'] ) ): ?>
                    <tr>
                        <td class="publications__icon">
                            <a href="<?php echo esc_url( $publication['attachment']['link_url'] ); ?>" target="_blank">
                                <img src="<?php echo esc_url( $publication['attachment']['link_icon'] ); ?>" alt="">
                            </a>
                        </td>
                        <td class="publications__text">
                            <a href="<?php echo esc_url( $publication['attachment']['link_url'] ); ?>" target="_blank">
                                <?php echo esc_attr( $publication['attachment']['link_title'] ); ?>
                            </a>
                        </td>
                    </tr>
                    <?php endif; ?>
                    <?php else: ?>
                    <tr>
                        <td class="publications__text">
                            <?php if ( ! empty( $publication['publication_permalink'] ) ) : ?>
                                <a href="<?php echo esc_url( $publication['publication_permalink'] ); ?>" >
                                    <?php echo $publication['attachment']['rec_massage'] ?>
                                </a>
                            <?php else: ?>
                                <?php echo $publication['attachment']['rec_massage'] ?>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td class="publications__text">
                            <?php if ( ! empty( $publication['publication_permalink'] ) ) : ?>
                                <a href="<?php echo esc_url( $publication['publication_permalink'] ); ?>" >
                                    <span><?php fev3_component_date(); ?></span>
                                </a>
                            <?php else: ?>
                                <span><?php fev3_component_date(); ?></span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <?php endif; ?>
                </table>
                <?php endif; ?>
            </columns>
        </row>
        <spacer size="34"></spacer>
        <?php endforeach; ?>
        <?php endif; ?>
    {% endraw %}
</container>
