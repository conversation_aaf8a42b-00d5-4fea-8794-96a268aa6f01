{% raw %}
    <f:if condition="{panels}">
        <f:if condition="{panels -> f:count()}">
            <container class="section-item">
                <table role="presentation" class="quick-access-table row">
                    <tr>
                        <f:for each="{panels}" as="panel" iteration="iteration">
                            <f:if condition="!{iteration.isFirst}"><th style="width: 8px;" class="quick-access-spacer"></th></f:if>
                            <th class="small-12 large-2 columns{f:if(condition:'{iteration.isFirst}',then:' first')}{f:if(condition:'{iteration.isLast}',then:' last')} quick-access-item" valign="top">
                                <table>
                                    <tr>
                                        <td class="quick-access-icon">
                                            <f:if condition="{panel.panel.link} && {panel.panel.picto}">
                                                <f:then><center><a href="{f:uri.typolink(parameter:panel.panel.link,absolute:1)}"><img src="{f:uri.image(src:panel.panel.picto,absolute:1)}" alt=""></a></center></f:then>
                                                <f:else if="{panel.panel.picto}"><center><img src="{f:uri.image(src:panel.panel.picto,absolute:1)}" alt=""></center></f:else>
                                                <f:else><center>&nbsp;</center></f:else>
                                            </f:if>
                                        </td>
                                    </tr>
                                    <tr><td><spacer size="5"></spacer></td></tr>
                                    <tr>
                                        <td>
                                            <f:if condition="{panel.panel.link} && {panel.panel.linkTitle}">
                                                <f:then><center><a href="{f:uri.typolink(parameter:panel.panel.link,absolute:1)}">{panel.panel.linkTitle}</a></center></f:then>
                                                <f:else if="{panel.panel.linkTitle}"><center>{panel.panel.linkTitle}</center></f:else>
                                                <f:else><center>&nbsp;</center></f:else>
                                            </f:if>
                                        </td>
                                    </tr>
                                </table>
                            </th>
                        </f:for>
                    </tr>
                </table>
                <spacer size="90"></spacer>
            </container>
        </f:if>
    </f:if>
{% endraw %}
