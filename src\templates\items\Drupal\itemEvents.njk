<container>
    {% raw %}
        {% set item = drupal_entity('node', array.0.target_id, 'newsletter_focus')['#node'] %}
        {% if item %}
            {% set link = item.id() ? path('entity.node.canonical', {'node': item.id()}) : NULL %}
            {% set link = link|path_to_absolute %}
            {% set image = drupal_field('field_media_image', 'node', item.id(), 'newsletter_focus').0['#image_url'] %}
            {% set image_alt = drupal_field('field_media_image', 'node', item.id(), 'newsletter_focus').0['#alt'] %}
            {% set theme = drupal_field('field_categories', 'node', item.id(), 'newsletter_focus') %}
            {% set place_node = drupal_field('field_node', 'node', item.id(), 'newsletter_focus').0['#plain_text'] %}

            {% set start = item.field_date_range.start_date %}
            {% set end = item.field_date_range.end_date %}

            {% set start_date = start|date("d") %}
            {% set start_month = start|date("M") %}
            {% set end_date = end|date("d") %}
            {% set end_month = end|date("M") %}
            <row>
                <columns class="first last events-focus">
                    {% if image %}
                        <a href="{{ link }}" class="events-image" aria-hidden="true" tabindex="-1">
                            <img src="{{ image }}" alt="{{ image_alt }}">
                        </a>
                    {% endif %}
                    {% if start_date %}
                        <table role="presentation" class="events-date">
                        <tr>
                            <td colspan="12">
                                <spacer size="15"></spacer>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <table>
                                    <tr>
                                        <td>
                                            <p class="date date--first"><span>{{ start_date }}</span></p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <p class="date date--first">{{ start_month }}</p>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                            {% if start|date("Ymd") != end|date("Ymd") %}
                            <td style="width: 18px; vertical-align: middle; min-width: 18px;" class="divider"><img src="{{ site_url }}/images/icons/angle-right-white.png" alt></td>
                            <td>
                                <table>
                                    <tr>
                                        <td>
                                            <p class="date date--first"><span>{{ end_date }}</span></p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <p class="date date--first">{{ end_month }}</p>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                            {% endif %}
                            <td style="width: 80%;">&nbsp;</td>
                        </tr>
                        <tr>
                            <td colspan="12">
                                <spacer size="15"></spacer>
                            </td>
                        </tr>
                    </table>
                    {% endif %}
                    <spacer size="20"></spacer>
                    {% if theme.0 %}
                        <table role="presentation" class="category"><tr><td class="category">{{ theme.0 }}</td></tr></table>
                        <spacer size="10"></spacer>
                    {% endif %}
                    <h3 class="section__title"><a href="{{ link }}">{{ item.title.value }}</a></h3>
                    <spacer size="15"></spacer>
                    <table role="presentation" class="events__timeplace">
                        {% if start|date("H:i") and start|date("H:i") != "00:00" %}
                        <tr>
                            <td class="events__icon"><img src="{{ site_url }}/images/icons/clock.png" alt="{{ 'Timetable'|t }}"></td>
                            <td class="events__time">
                                {{ start|date("H") ~ 'h' ~ start|date("i") }}
                                {% if end|date("H:i") and end|date("H:i") != start|date("H:i") %}
                                   - {{ end|date("H") ~ 'h' ~ end|date("i") }}
                                {% endif %}
                            </td>
                        </tr>
                        {% endif %}
                        {% if place_node %}
                            <tr>
                                <td class="events__icon"><img src="{{ site_url }}/images/icons/map-marker-alt.png" alt="{{ 'Place'|t }}"></td>
                                <td class="events__place">{{ place_node }}</td>
                            </tr>
                        {% endif %}
                    </table>
                    <spacer size="14"></spacer>
                </columns>
            </row>
        {% endif %}
    {% endraw %}
    {% raw %}
    {% for item in array[1:] %}
        {% set item = drupal_entity('node', item.target_id, 'newsletter')['#node'] %}
        {% if item %}
            {% set link = item.id() ? path('entity.node.canonical', {'node': item.id()}) : NULL %}
            {% set link = link|path_to_absolute %}
            {% set image = drupal_field('field_media_image', 'node', item.id(), 'newsletter').0['#image_url'] %}
            {% set image_alt = drupal_field('field_media_image', 'node', item.id(), 'newsletter').0['#alt'] %}
            {% set theme = drupal_field('field_categories', 'node', item.id(), 'newsletter') %}
            {% set place_node = drupal_field('field_node', 'node', item.id(), 'newsletter').0['#plain_text'] %}

            {% set start = item.field_date_range.start_date %}
            {% set end = item.field_date_range.end_date %}

            {% set start_date = start|date("d") %}
            {% set start_month = start|date("M") %}
            {% set end_date = end|date("d") %}
            {% set end_month = end|date("M") %}
            <row class="events-item">
            <columns large="6" class="first">
                <table role="presentation">
                    <tr>
                        {% if image %}
                            <td class="events-image">
                                <a href="{{ link }}" aria-hidden="true" tabindex="-1">
                                    <img src="{{ image }}" alt="{{ image_alt }}">
                                </a>
                            </td>
                        {% endif %}
                        {% if start_date %}
                            <td style="width: 100px;" class="events-date">
                            <a href="{{ link }}">
                                <table>
                                    <tr>
                                        <td>
                                            <spacer size="30"></spacer>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <p class="date date--first"><span>{{ start_date }}</span></p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <p class="date date--first">{{ start_month }}</p>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <spacer size="7"></spacer>
                                        </td>
                                    </tr>
                                    {% if start|date("Ymd") != end|date("Ymd") %}
                                        <tr>
                                            <td>
                                                <center>
                                                    <img src="{{ site_url }}/images/icons/angle-down.png" alt class="float-center" align="center">
                                                </center>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <spacer size="7"></spacer>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <p class="date"><span>{{ end_date }}</span></p>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <p class="date">{{ end_month }}</p>
                                            </td>
                                        </tr>
                                    {% endif %}
                                    <tr>
                                        <td>
                                            <spacer size="10"></spacer>
                                        </td>
                                    </tr>
                                </table>
                            </a>
                        </td>
                        {% endif %}
                    </tr>
                </table>
            </columns>
            <columns large="6" class="last">
                {% if theme.0 %}
                    <table role="presentation" class="category"><tr><td>{{ theme.0 }}</td></tr></table>
                    <spacer size="10"></spacer>
                {% endif %}
                <h3 class="section__title"><a href="{{ link }}">{{ item.title.value }}</a></h3>
                <spacer size="15"></spacer>
                <table role="presentation" class="events__timeplace">
                    {% if start|date("H:i") and start|date("H:i") != "00:00" %}
                        <tr>
                            <td class="events__icon"><img src="{{ site_url }}/images/icons/clock.png" alt="{{ 'Timetable'|t }}"></td>
                            <td class="events__time">
                                {{ start|date("H") ~ 'h' ~ start|date("i") }}
                                {% if end|date("H:i") and end|date("H:i") != start|date("H:i") %}
                                - {{ end|date("H") ~ 'h' ~ end|date("i") }}
                                {% endif %}
                            </td>
                        </tr>
                    {% endif %}
                    {% if place_node %}
                        <tr>
                            <td class="events__icon"><img src="{{ site_url }}/images/icons/map-marker-alt.png" alt="{{ 'Place'|t }}"></td>
                            <td class="events__place">{{ place_node }}</td>
                        </tr>
                    {% endif %}
                </table>
            </columns>
        </row>
        {% endif %}
    {% endfor %}
    {% endraw %}
</container>
