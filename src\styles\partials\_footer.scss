.footer {
    background-color: $color-1--2;
    width: 100%;
    //background-image: url('#{$img-path}footer-shadow.png');
    //background-position: center top;
    //background-size: 100% auto;
    //background-repeat: no-repeat;

    .sr-only {
       font-size: 0;
    }

    .container {
        background: none;
    }

    .small-12.large-8 {
        padding-left: 50px;
        padding-top: 40px;

        @media only screen and (max-width: #{$global-breakpoint}) {
            padding-left: 0 !important;
            padding-top: 0 !important;
        }
    }

    .text-big {
        p {
            color: $color-white;
            font-size: 11px;
            margin-bottom: 0;
        }

        a {
            color: white;
            text-decoration: underline;
        }
        
        .archi {
            padding-right: 12px !important;
            text-decoration: none !important;
        }

        .désins {
            padding-left: 12px !important;
            text-decoration: none !important;
        }

        @media screen {
            a {
                color: white;
                text-decoration: underline;
            }
        }
    }

    th.columns table.footer-social {
        margin: 0;

        @media only screen and (max-width: #{$global-breakpoint}) {
            text-align: center;
        }

        tr {
            text-align: center;
        }

        .footer-social__item {
            padding-right: 5px;
            vertical-align: middle;
            width: 37px;

            @media only screen and (max-width: #{$global-breakpoint}) {
                display: inline-block;
            }
        }

        a {
            display: block;
            text-align: center;
        }

        img {
            margin: 0 auto;
            Margin: 0 auto;
        }
    }

    .footer-aside.columns {
        @media only screen and (max-width: #{$global-breakpoint}) {
            padding-left: 20px !important;
            padding-right: 20px !important;
        }

        &__logo {
            display: inline-block;
            max-width: none;
            width: 196px;

            @media only screen and (max-width: #{$global-breakpoint}) {
                display: block;
                width: auto;
            }
        }
    }

    .footer-title {
        Margin-bottom: 12px;
        Margin-top: 22px;
        color: $color-white;
        font-size: 18px;
        line-height: (22/18);
        margin-bottom: 12px;
        margin-top: 22px;
    }

    .footer-address {
        color: $color-white;
        font-size: 14px;
        line-height: (18/14);
        margin-bottom: 0;
        Margin-bottom: 0;
    }

    table.button.footer-email {
        margin-bottom: 0 !important;
        margin-top: 0 !important;

        @media only screen and (max-width: #{$global-breakpoint}) {
            margin-left: auto !important;
            margin-right: auto !important;
        }

        table td {
            background: none;
            border: 1px solid $color-white;
            color: $color-white;
            font-size: 12px;
            font-weight: 400;
            letter-spacing: 1.2px;
            line-height: 1;


            a {
                color: $color-white;
                font-size: 12px;
                font-weight: 400;
                letter-spacing: 1.2px;
                line-height: 1;
                padding: {
                    bottom: 14px;
                    left: 35px;
                    right: 35px;
                    top: 15px;
                };
            }
        }

        img {
            display: inline;
            vertical-align: bottom;
        }
    }
}
