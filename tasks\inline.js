const fs = require('fs');
const gulp = require('gulp');
const changeCase = require('change-case');
const siphon = require('siphon-media-query');
const CleanCSS = require('clean-css');
const Merge = require('merge-stream');
const $ = require('./helpers');

const responsiveTemplate = {
    typo3(css) {
        return `<html data-namespace-typo3-fluid="true"
xmlns="http://www.w3.org/1999/xhtml" lang="fr"
xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
>
<f:layout name="Page"/>
<f:section name="NewsletterStyles">
    <style>
        ${css}
    </style>
</f:section>
</html>`;
    },
    drupal(css) {
        return `<style>${css}</style>`;
    },
    wordpress(css) {
        return `<style>${css}</style>`;
    },
};

// const zVarExp = str => str.replace(new RegExp('(z-var\\s*:\\s*)(\\w*)(.*;)', 'g'),
// (match, g1, g2) => `{${g2}}`);

// eslint-disable-next-line no-useless-escape
const zVarExp = (str) => {
    const regExp = /(z-var\s*:\s*)(.*);/mg;

    return str.replace(regExp, (match, g1, g2) => {
        if (g2) {
            const propsArray = g2.split(';');

            if (propsArray.length > 1) {
                const [sfProps] = propsArray;

                if (sfProps.includes('sf-')) {
                    propsArray[0] = sfProps.replace(/(sf-)/g, '').replace(/\|/g, ';');
                } else {
                    propsArray[0] = `{${sfProps}}`;
                }

                return propsArray.join(';');
            }

            return `{${g2}}`;
        }

        return g2;
    });
};

// Inline CSS into HTML, adds media query CSS into the <style> tag of the email,
// and compresses the HTML
const inline = (done) => {
    if (!$.IS_INLINE) {
        return done();
    }

    // get all css code from a file
    let mediaCSS = fs.readFileSync(`${$.destHTML}app.css`).toString();

    // cut them and leave only @media css
    mediaCSS = siphon(mediaCSS);

    // minify that styles
    mediaCSS = new CleanCSS().minify(mediaCSS).styles;

    const stream = new Merge();

    stream.add(
        gulp.src(`${$.destHTML}*${$.htmlExt}`)
            .pipe($.plugin.inlineCss({
                applyStyleTags: false,
                codeBlocks: {
                    tsBlocks: {
                        start: '{',
                        end: '}',
                    },
                },
            }))
            .pipe($.plugin.injectString.replace('<!-- <style> -->', `<style>${mediaCSS}</style>`))
            .pipe($.plugin.injectString.custom(zVarExp)) // Custom method. See README.md
            .pipe($.plugin.if(!$.IS_NO_INKY, gulp.dest($.destHTML))),
    );

    if ($.IS_BE) {
        stream.add($.plugin.file(`NewsletterCSS${$.htmlExt}`, responsiveTemplate[$.env](mediaCSS), { src: true })
            .pipe($.plugin.if($.IS_DRUPAL, $.plugin.rename((path) => {
                path.basename = `${changeCase.paramCase(path.basename.slice(0, -5))}.html`;
            })))
            .pipe($.plugin.if($.IS_WORDPRESS, $.plugin.rename((path) => {
                const newBaseName = changeCase.sentenceCase(path.basename).toLowerCase();
                path.basename = changeCase.paramCase(newBaseName);
            })))
            .pipe(gulp.dest($.dest($.config.styles.file))));
    }

    return stream;
};

module.exports = inline;
