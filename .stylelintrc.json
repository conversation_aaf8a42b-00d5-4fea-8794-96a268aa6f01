{"extends": "stylelint-config-sass-guidelines", "plugins": ["stylelint-scss", "stylelint-order"], "rules": {"color-hex-length": "long", "indentation": 4, "max-nesting-depth": 4, "order/order": ["declarations", "custom-properties"], "selector-no-qualifying-type": [true, {"ignore": ["attribute", "class"]}], "selector-class-pattern": "^((_|-){0,2}[a-z]+)*", "at-rule-blacklist": null, "selector-max-compound-selectors": 10}}