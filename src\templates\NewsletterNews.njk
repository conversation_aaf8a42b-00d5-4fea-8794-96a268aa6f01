{% import "helpers/mixins.njk" as mixins with context -%}
{{- mixins.includeCss(isTemplateIncluded) -}}
{% set news = {
    title: '{title}||{{ title }}||<?php echo esc_html( $args[\'block_title\'] ); ?>||On en parle',
    footerLink: '{allLink}||{{ link_list }}||<?php echo esc_url( $args[\'archive_link\'] ); ?>||#',
    footerText: '{allLinkTitle}||Toutes les actualités||<?php echo esc_html( $args[\'archive_link_title\'] ); ?>||Toutes les actualités'
} | parseBE -%}
<center>
    <wrapper class="section news">
        {{- mixins.sectionTitle(news.title) -}}
        {% include "items/{{ env }}/itemNews.njk" | interpolate -%}
        {{- mixins.sectionButton(news.footerText, news.footerLink) -}}
    </wrapper>
</center>
