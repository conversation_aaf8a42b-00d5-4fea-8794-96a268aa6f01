const gulp = require('gulp');
const browser = require('browser-sync');
const $ = require('./helpers');

// Compile Sass into CSS
const styles = () => gulp.src($.config.styles.src)
    .pipe($.plugin.plumber({
        errorHandler: $.errorHandler,
    }))
    .pipe($.plugin.injectString.prepend(`$img-path: "${$.parse($.imgPath)}";`))
    .pipe($.plugin.if(!$.IS_INLINE, $.plugin.sourcemaps.init()))
    .pipe($.plugin.sass({
        includePaths: $.config.styles.includePaths,
    }))
    .pipe($.plugin.if(!$.IS_INLINE, $.plugin.sourcemaps.write()))
    .pipe($.plugin.groupCssMediaQueries())
    .pipe(gulp.dest($.dest($.config.styles.dest)))
    .pipe($.plugin.if(!$.IS_INLINE, browser.stream()));

module.exports = styles;
