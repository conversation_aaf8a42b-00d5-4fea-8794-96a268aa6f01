{% raw %}
    <container class="section-item">
        <table role="presentation" class="quick-access-table row">
            <tr>
                {% for item in array %}
                {% set image_file = drupal_field('field_media_icon', 'paragraph', item.target_id).0 %}
                {% set image = image_file['#image_url'] %}
                {% set image_id = image_file['#target_id'] %}
                {% set image_alt = image_file['#alt'] %}
                {% set link = drupal_field('field_link', 'paragraph', item.target_id).0 %}
                {% set link_title = link['#title'] %}
                {% set link_url = link['#url'] %}
                {% set external = link_url.external %}

                {% if '.svg' in image %}
                    {% set image = svg_to_png(image, image_id, 35, 35) %}
                {% endif %}

                <th class="small-12 large-2 columns{{ loop.first ? ' first'  }}{{ loop.last ? ' last' }} quick-access-item" valign="top">
                    <a href="{{ external ? link_url : link_url|render|path_to_absolute }}">
                        <center>
                            <img src="{{ image }}" {% if image_alt %} alt="{{ image_alt }}" {%else%}alt{%endif%}>
                        </center>
                        <spacer size="5"></spacer>
                        <center>
                            <span><font style="color: #ffffff;">{{- link_title -}}</font></span>
                        </center>
                    </a>
                </th>
                {% if not loop.last %}
                <th style="width: 8px;" class="quick-access-spacer"></th>
                {% endif %}
                {% endfor %}
            </tr>
        </table>
        <spacer size="90"></spacer>
    </container>
{% endraw %}
