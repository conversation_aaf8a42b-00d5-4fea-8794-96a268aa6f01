const gulp = require('gulp');
const inky = require('inky');
const changeCase = require('change-case');
const { loremIpsum } = require('lorem-ipsum');
const $ = require('./helpers');

const switchcase = cases => defaultCase => key => (Object.prototype.hasOwnProperty.call(cases, key) ? cases[key] : defaultCase);

const njkEnvironment = (env) => {
    env.addFilter('slug', str => str && str.replace(/\s/g, '-', str).toLowerCase());

    env.addFilter('interpolate', str => env.renderString(str));

    env.addFilter('parseBE', (object) => {
        const getByEnv = switchcase({
            Typo3: arr => arr[0],
            Drupal: arr => arr[1],
            Wordpress: arr => arr[2],
        })(arr => env.renderString(arr[3]));

        const iterate = (val) => {
            if (typeof val === 'object') {
                if (Array.isArray(val)) {
                    return val.map(v => iterate(v));
                }

                const newObj = {};
                Object.entries(val).forEach(([key, v]) => {
                    newObj[key] = iterate(v);
                });
                return newObj;
            }

            if (typeof val === 'string') {
                const str = val.split('||');
                return str.length === 4 ? getByEnv($.templateEnv)(str) : str;
            }

            return val;
        };

        return iterate(object);
    });

    env.addGlobal('isTYPO3', $.IS_TYPO3);
    env.addGlobal('isDRUPAL', $.IS_DRUPAL);
    env.addGlobal('isWORDPRESS', $.IS_WORDPRESS);
    env.addGlobal('isBE', $.IS_BE);
    env.addGlobal('env', $.templateEnv);

    env.addGlobal('imgPath', $.parse($.imgPath));

    env.addGlobal('picsumIds', $.config.picsumIds);

    env.addGlobal('lorem', (count = '1', units = 'sentences', makeSentence = false, sentenceLowerBound = 5, sentenceUpperBound = 15, format = 'plain') => {
        const capitalize = str => str.charAt(0).toUpperCase() + str.slice(1);
        const str = loremIpsum({
            // More options here: https://www.npmjs.com/package/lorem-ipsum
            count, // Number of words, sentences, or paragraphs to generate.
            units, // Generate 'words', 'sentences', or 'paragraphs'.
            sentenceLowerBound, // Minimum words per sentence.
            sentenceUpperBound, // Maximum words per sentence.
            format, // 'plain' or 'html'
        });
        return units === 'words' && makeSentence ? `${capitalize(str)}.` : str;
    });

    env.addFilter('debug', str => console.log('[DEBUG_NJK]:', str));

    env.addFilter('unique', str => `${str}-${Math.random() * 0xffffff | 0}`);
};

const noInkyforBE = $.IS_NO_INKY && $.IS_BE;

const templates = () => gulp.src($.config.templates.src[$.IS_BE ? 'be' : 'fe'])
    .pipe($.plugin.plumber({
        errorHandler: $.errorHandler,
    }))
    .pipe($.plugin.nunjucksRender({
        path: $.config.templates.envPath,
        ext: $.htmlExt,
        envOptions: { autoescape: false },
        manageEnv: njkEnvironment, // docs https://github.com/carlosl/gulp-nunjucks-render#environment
    }))
    .pipe($.plugin.if(!$.IS_NO_INKY, inky()))
    .pipe($.plugin.if($.IS_DRUPAL, $.plugin.rename((path) => {
        path.basename = `${changeCase.paramCase(path.basename.slice(0, -5))}.html`;
    })))
    .pipe($.plugin.if($.IS_WORDPRESS, $.plugin.rename((path) => {
        const newBaseName = changeCase.sentenceCase(path.basename).toLowerCase();
        path.basename = changeCase.paramCase(newBaseName);
    })))
    .pipe($.plugin.if(!noInkyforBE, gulp.dest($.destHTML)));

module.exports = templates;
