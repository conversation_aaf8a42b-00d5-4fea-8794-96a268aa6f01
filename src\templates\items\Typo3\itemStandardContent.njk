{% raw %}
    <f:if condition="{panels}">
        <f:if condition="{panels -> f:count()}">
            <container class="section-item">
                <f:for each="{panels}" as="panel" iteration="iteration">
                    <row>
                        <f:if condition="{panel.panel.image}">
                            <f:then>
                                <columns large="6" class="first section-image">
                                    <f:if condition="{panel.panel.link} && {f:uri.typolink(parameter:panel.panel.link,absolute:1)}">
                                        <f:then>
                                            <a href="{f:uri.typolink(parameter:panel.panel.link,absolute:1)}">
                                                <img src="{f:uri.image(src:panel.panel.image,absolute:1,width:'290c',height:'190c')}" alt="">
                                            </a>
                                        </f:then>
                                        <f:else>
                                            <img src="{f:uri.image(src:panel.panel.image,absolute:1,width:'290c',height:'190c')}" alt="">
                                        </f:else>
                                    </f:if>
                                </columns>
                                <columns large="6" class="last section-content">
                                    <h3 class="section__title">
                                        <f:if condition="{panel.panel.link} && {f:uri.typolink(parameter:panel.panel.link,absolute:1)}">
                                            <f:then>
                                                <a href="{f:uri.typolink(parameter:panel.panel.link,absolute:1)}">{panel.panel.title}</a>
                                            </f:then>
                                            <f:else>
                                                {panel.panel.title}
                                            </f:else>
                                        </f:if>
                                    </h3>
                                    <spacer size="15"></spacer>
                                    <p class="section__teaser">
                                        <f:if condition="{panel.panel.link} && {f:uri.typolink(parameter:panel.panel.link,absolute:1)}">
                                            <f:then>
                                                <a href="{f:uri.typolink(parameter:panel.panel.link,absolute:1)}">{panel.panel.teaser -> f:format.nl2br() -> f:format.raw()}</a>
                                            </f:then>
                                            <f:else>
                                                {panel.panel.teaser -> f:format.nl2br() -> f:format.raw()}
                                            </f:else>
                                        </f:if>
                                    </p>
                                </columns>
                            </f:then>
                            <f:else>
                                <columns large="12" class="first last section-content">
                                    <h3 class="section__title">
                                        <f:if condition="{panel.panel.link} && {f:uri.typolink(parameter:panel.panel.link,absolute:1)}">
                                            <f:then>
                                                <a href="{f:uri.typolink(parameter:panel.panel.link,absolute:1)}">{panel.panel.title}</a>
                                            </f:then>
                                            <f:else>
                                                {panel.panel.title}
                                            </f:else>
                                        </f:if>
                                    </h3>
                                    <spacer size="15"></spacer>
                                    <p class="section__teaser">
                                        <f:if condition="{panel.panel.link} && {f:uri.typolink(parameter:panel.panel.link,absolute:1)}">
                                            <f:then>
                                                <a href="{f:uri.typolink(parameter:panel.panel.link,absolute:1)}">{panel.panel.teaser -> f:format.nl2br() -> f:format.raw()}</a>
                                            </f:then>
                                            <f:else>
                                                {panel.panel.teaser -> f:format.nl2br() -> f:format.raw()}
                                            </f:else>
                                        </f:if>
                                    </p>
                                </columns>
                            </f:else>
                        </f:if>
                    </row>
                    <spacer size="20"></spacer>
                </f:for>
            </container>
        </f:if>
    </f:if>
{% endraw %}
