<f:if condition="{panels}">
    <f:if condition="{panels -> f:count()}">
        <table role="presentation" class="footer-social">
            <tr>
                <f:for each="{panels}" as="panel" iteration="iteration">
                    <f:if condition="{iteration.isFirst}">
                        <f:then>
                            <f:if condition="{panel.panel.picto}">
                                {f:uri.image(absolute:1,src:panel.panel.picto) -> v:variable.set(name: 'image')}
                                <td class="footer-social__item -first">
                                    <a href="{f:uri.typolink(parameter:panel.panel.link)}" title="" target="_blank">
                                        <center>
                                            <img src="{image}" alt="">
                                        </center>
                                    </a>
                                </td>
                            </f:if>
                        </f:then>
                        <f:else>
                            <f:if condition="{panel.panel.picto}">
                                {f:uri.image(absolute:1,src:panel.panel.picto) -> v:variable.set(name: 'image')}
                                <td class="footer-social__item">
                                    <a href="{f:uri.typolink(parameter:panel.panel.link)}" title="" target="_blank">
                                        <center>
                                            <img src="{image}" alt="">
                                        </center>
                                    </a>
                                </td>
                            </f:if>
                        </f:else>
                    </f:if>
                </f:for>
                <td>&nbsp;</td>
            </tr>
        </table>
    </f:if>
</f:if>
