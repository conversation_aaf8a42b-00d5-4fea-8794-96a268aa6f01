//  Foundation for Emails Settings
//  ------------------------------
//
//  Table of Contents:
//
//   1. Global
//   2. Grid
//   3. Block Grid
//   4. Typography
//   5. Button
//   6. Callout
//   7. Menu
//   8. Thumbnail

// 0. Site specific
// ---------

// ## color-1
$color-1--1: #ac1572 !default; // Color 1 from palette in design
$color-1--2: #1f1f1f !default; // Color 2 from palette in design
$color-1--3: #e6b9d5 !default; // color-1--2 at 30% transparency on white
$color-1--4: #780f50 !default; // color-1--2 at 70% transparency on black

// ## color-2
$color-2--1: #4bbdeb !default; // Color 3 from palette in design
$color-2--2: #3484a4 !default; // color-2--1 at 70% transparency on black
$color-2--3: #c9ebf9 !default; // color-2--1 at 30% transparency on white

// ## Color-3
$color-3--1: #f6f6f6 !default;
$color-3--2: #c5c5c5 !default;
$color-3--3: #707070 !default;
$color-3--4: #666666 !default;
$color-3--5: #aab6a8 !default;
$color-3--6: #f7f7f7 !default;

$color-red: #fe0000;
$color-green: #278417;
$alert-background: #fcdcde;
$alert-title: #ff000d;
$color-white: #ffffff;
$color-black: #000000;
$typo-1: Arial, Helvetica, sans-serif;

$img-path: '' !default; // Default path for local images

// 1. Global
// ---------


$primary-color: $color-1--2;
//$secondary-color: #888888;
//$success-color: #3adb76;
//$warning-color: #ffae00;
//$alert-color: #ec5840;
//$light-gray: #f3f3f3;
$anchor-color: $color-black;
//$text-light-color: #888888;
//$medium-gray: #cacaca;
//$dark-gray: #8a8a8a;
//$black: #0a0a0a;
//$white: #fefefe;
//$pre-color: #ff6908;
$global-width: 652px;
$global-width-small: 100%;
$global-gutter: 16px;
$body-background: $color-white;
//$container-background: $white;
//$global-font-color: $black;
$body-font-family: $typo-1;
//$global-padding: 16px;
//$global-margin: 16px;
$global-radius: 0;
//$global-rounded: 500px;
//$global-breakpoint: $global-width + $global-gutter;

// 2. Grid
// -------

$grid-column-count: 12;
//$column-padding-bottom: 16px;

// 3. Block Grid
// -------------

//$block-grid-max: 8;
//$block-grid-gutter: $global-gutter;

// 4. Typography
// -------------

$global-font-weight: 400;
$header-color: $color-black;
$global-line-height: 1.5;
$global-font-size: 14px;
//$body-line-height: $global-line-height;
//$header-font-family: $body-font-family;
$header-font-weight: 700;
$h1-font-size: 30px;
//$h2-font-size: 23px;
$h3-font-size: 30px;
//$h4-font-size: 18px;
//$h5-font-size: 20px;
//$h6-font-size: 18px;
//$header-margin-bottom: 10px;
//$paragraph-margin-bottom: 0;
//$small-font-size: 80%;
//$small-font-color: $medium-gray;
//$lead-font-size: $global-font-size * 1.25;
//$lead-line-height: 1.6;
//$text-padding: 10px;
//$subheader-lineheight: 1.4;
//$subheader-color: $dark-gray;
//$subheader-font-weight: 900;
//$subheader-margin-top: 4px;
//$subheader-margin-bottom: 8px;
$hr-width: 30px;
$hr-border: 3px solid $color-1--2;
$hr-margin: 0 0 10px;
//$anchor-text-decoration: none;
//$anchor-color: inherit;
//$anchor-color-visited: $anchor-color;
//$anchor-color-hover: darken($primary-color, 10%);
//$anchor-color-active: $anchor-color-hover;
//$stat-font-size: 40px;

// 5. Button
// ---------

$button-padding: (
    tiny: 4px 8px 4px 8px,
    small: 5px 10px 5px 10px,
    default: 8px 16px 8px 16px,
    large: 12px 29px 12px 29px,
);

$button-font-size: (
    tiny: 10px,
    small: 12px,
    default: 16px,
    large: 12px,
);

$button-color: $color-white;
//$button-color-alt: $medium-gray;
//$button-font-weight: bold;
//$button-margin: 0 0 $global-margin 0;
$button-background: $color-1--2;
$button-border: 1px solid $color-1--2;
$button-radius: $global-radius * 10;
//$button-rounded: $global-rounded * 10;

// 6. Callout
// ----------

//$callout-background: $white;
//$callout-background-fade: 85%;
//$callout-padding: 10px;
//$callout-margin-bottom: $global-margin;
//$callout-border: 1px solid darken($callout-background, 20%);
//$callout-border-secondary: 1px solid darken($secondary-color, 20%);
//$callout-border-success: 1px solid darken($success-color, 20%);
//$callout-border-warning: 1px solid darken($warning-color, 20%);
//$callout-border-alert: 1px solid darken($alert-color, 20%);

// 7. Menu
// -------

//$menu-item-padding: 10px;
//$menu-item-gutter: 10px;
//$menu-item-color: $primary-color;

// 8. Thumbnail
// ------------

//$thumbnail-border: solid 4px $white;
//$thumbnail-margin-bottom: $global-margin;
//$thumbnail-shadow: 0 0 0 1px rgba($black, 0.2);
//$thumbnail-shadow-hover: 0 0 6px 1px rgba($primary-color, 0.5);
//$thumbnail-transition: box-shadow 200ms ease-out;
//$thumbnail-radius: $global-radius;
